package eu.torvian.chatbot.app.service.api.ktor

import arrow.core.Either
import arrow.core.left
import eu.torvian.chatbot.app.utils.misc.KmpLogger
import eu.torvian.chatbot.app.utils.misc.ioDispatcher
import eu.torvian.chatbot.app.utils.misc.kmpLogger
import eu.torvian.chatbot.common.api.ApiError
import eu.torvian.chatbot.app.repository.RepositoryError
import eu.torvian.chatbot.app.repository.toRepositoryError
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.plugins.*
import io.ktor.client.statement.*
import io.ktor.serialization.*
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.withContext
import kotlinx.io.IOException

/**
 * Abstract base class for repository-aware API clients that use the new RepositoryError hierarchy.
 *
 * This class provides comprehensive error handling that translates network and API responses
 * into structured [RepositoryError] types, offering better error categorization than the
 * original [BaseApiClient] which only handles [ApiError].
 *
 * The [safeApiCall] helper function handles:
 * - Network connectivity issues ([RepositoryError.NetworkError])
 * - Server-side errors ([RepositoryError.ServerError] wrapping [ApiError])
 * - Data serialization/parsing problems ([RepositoryError.SerializationError])
 * - Unexpected system errors ([RepositoryError.UnknownError])
 *
 * This class is designed for the migrated features (Model, Provider, Settings) and
 * coexists with the original [BaseApiClient] for non-migrated features.
 *
 * @property client The Ktor HttpClient instance to use for making API calls.
 */
abstract class RepositoryAwareBaseApiClient(protected val client: HttpClient) {
    
    companion object {
        private val logger: KmpLogger = kmpLogger<RepositoryAwareBaseApiClient>()
    }

    /**
     * Executes a suspend block (typically a Ktor HttpClient call) and wraps the result
     * in an [Either] of [RepositoryError] or [T].
     *
     * This method provides comprehensive error handling by categorizing different types
     * of failures into appropriate [RepositoryError] subtypes:
     *
     * - HTTP response errors are handled by attempting to deserialize [ApiError] from
     *   the response body and wrapping it in [RepositoryError.ServerError]
     * - Network I/O errors are wrapped in [RepositoryError.NetworkError]
     * - Serialization/deserialization errors are wrapped in [RepositoryError.SerializationError]
     * - All other unexpected errors are wrapped in [RepositoryError.UnknownError]
     * - [CancellationException] is re-thrown to preserve coroutine cancellation semantics
     *
     * @param T The expected type of the successful result.
     * @param block The suspend function block containing the Ktor HttpClient call.
     * @return An [Either] containing a [RepositoryError] on the left or the successful result [T] on the right.
     */
    protected suspend fun <T> safeApiCall(
        block: suspend () -> T
    ): Either<RepositoryError, T> {
        return withContext(ioDispatcher) {
            try {
                Either.Right(block())
            } catch (e: ClientRequestException) {
                logger.warn("ClientRequestException: Status ${e.response.status}, Body: ${e.response.bodyAsText()}")
                tryDeserializeApiError(e.response, e)
            } catch (e: ServerResponseException) {
                logger.warn("ServerResponseException: Status ${e.response.status}, Body: ${e.response.bodyAsText()}")
                tryDeserializeApiError(e.response, e)
            } catch (e: RedirectResponseException) {
                logger.warn("RedirectResponseException: Status ${e.response.status}")
                tryDeserializeApiError(e.response, e)
            } catch (e: ResponseException) {
                logger.warn("Unexpected ResponseException: Status ${e.response.status}, Body: ${e.response.bodyAsText()}")
                RepositoryError.UnknownError("HTTP Response Error: ${e.message}", e).left()
            } catch (e: IOException) {
                logger.warn("IOException (Network Error): ${e.message}")
                RepositoryError.NetworkError(e).left()
            } catch (e: ContentConvertException) {
                logger.warn("ContentConvertException (Serialization Error): ${e.message}")
                RepositoryError.SerializationError("Data Serialization/Deserialization Error: ${e.message}", e).left()
            } catch (e: CancellationException) {
                logger.warn("Request cancelled: ${e.message}")
                throw e // Re-throw cancellation to preserve coroutine cancellation semantics
            } catch (e: Exception) {
                logger.error("Unexpected Exception during API call: ${e.message}", e)
                RepositoryError.UnknownError(e.message ?: "An unexpected client error occurred", e).left()
            }
        }
    }

    /**
     * Helper to attempt deserializing an [ApiError] from a [ResponseException]'s body
     * and wrap it in a [RepositoryError.ServerError].
     *
     * If deserialization fails, falls back to appropriate [RepositoryError] types
     * based on the type of deserialization failure.
     *
     * @param response The HTTP response containing the error body
     * @param originalException The original response exception that triggered this error handling
     * @return An [Either.Left] containing the appropriate [RepositoryError]
     */
    private suspend fun <T> tryDeserializeApiError(
        response: HttpResponse,
        originalException: ResponseException
    ): Either<RepositoryError, T> {
        return try {
            val apiError = response.body<ApiError>()
            apiError.toRepositoryError().left()
        } catch (e: ContentConvertException) {
            logger.warn("ContentConvertException reading error body for status ${response.status.value}: ${e.message}")
            RepositoryError.SerializationError("Failed to parse server error response: ${e.message}", e).left()
        } catch (e: Exception) {
            logger.warn("Unexpected exception reading error body for status ${response.status.value}: ${e.message}")
            RepositoryError.UnknownError("Failed to process server error response: ${e.message}", e).left()
        }
    }
}
