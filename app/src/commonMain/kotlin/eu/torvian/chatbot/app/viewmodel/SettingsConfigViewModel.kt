package eu.torvian.chatbot.app.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import eu.torvian.chatbot.app.domain.contracts.*
import eu.torvian.chatbot.app.repository.ModelRepository
import eu.torvian.chatbot.app.repository.SettingsRepository
import eu.torvian.chatbot.app.repository.RepositoryError
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.ModelSettings
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

/**
 * Manages the UI state and logic for configuring LLM Model Settings Profiles (E4.S5, E4.S6).
 *
 * This ViewModel is completely type-agnostic and supports any ModelSettings type that has
 * corresponding form state implementations in SettingsFormState.
 *
 * It handles:
 * - Loading and displaying a list of available LLM models (for selection).
 * - Loading and displaying a list of settings profiles associated with a selected model (E4.S5).
 * - Managing the state for adding new settings profiles (E4.S5).
 * - Managing the state for editing existing settings profiles (E4.S6).
 * - Deleting settings profiles (E4.S5).
 * - Communicating with the backend via [SettingsRepository] and [ModelRepository].
 *
 * @constructor
 * @param settingsRepository The repository for Settings-related operations.
 * @param modelRepository The repository for Model-related operations (needed for model selection).
 * @param uiDispatcher The dispatcher to use for UI-related coroutines. Defaults to Main.
 *
 * @property settingsConfigState The state containing both models and settings data for unified state management.
 * @property selectedModel The currently selected LLM model. Settings profiles will be loaded for this model.
 * @property dialogState The current dialog state for the settings tab.
 */
class SettingsConfigViewModel(
    private val settingsRepository: SettingsRepository,
    private val modelRepository: ModelRepository,
    private val uiDispatcher: CoroutineDispatcher = Dispatchers.Main
) : ViewModel() {

    // --- Private State Properties ---

    private val _settingsConfigState = MutableStateFlow<DataState<RepositoryError, SettingsConfigData>>(DataState.Idle)
    private val _selectedModel = MutableStateFlow<LLMModel?>(null)
    private val _selectedSettings = MutableStateFlow<ModelSettings?>(null)
    private val _dialogState = MutableStateFlow<SettingsDialogState>(SettingsDialogState.None)

    // --- Public State Properties ---

    /**
     * The state containing both models and settings data for unified state management.
     */
    val settingsConfigState: StateFlow<DataState<RepositoryError, SettingsConfigData>> = _settingsConfigState.asStateFlow()

    /**
     * The currently selected LLM model. Settings profiles will be loaded for this model.
     * If null, no model is selected and no settings are displayed.
     */
    val selectedModel: StateFlow<LLMModel?> = _selectedModel.asStateFlow()

    /**
     * The currently selected settings profile in the master-detail UI.
     */
    val selectedSettings: StateFlow<ModelSettings?> = _selectedSettings.asStateFlow()

    /**
     * The current dialog state for the settings tab.
     * Manages which dialog (if any) should be displayed and contains dialog-specific form state.
     */
    val dialogState: StateFlow<SettingsDialogState> = _dialogState.asStateFlow()

    // --- Initialization ---

    init {
        // Set up reactive data streams by combining repository StateFlows
        viewModelScope.launch {
            combine(
                modelRepository.models,
                settingsRepository.settings
            ) { modelsState, settingsState ->
                when {
                    modelsState.isLoading || settingsState.isLoading -> DataState.Loading
                    modelsState.isError -> DataState.Error(modelsState.errorOrNull!!)
                    settingsState.isError -> DataState.Error(settingsState.errorOrNull!!)
                    modelsState.isSuccess && settingsState.isSuccess -> {
                        val configData = SettingsConfigData(
                            models = modelsState.dataOrNull!!,
                            settings = settingsState.dataOrNull!!
                        )
                        DataState.Success(configData)
                    }
                    else -> DataState.Idle
                }
            }.collect { combinedState ->
                _settingsConfigState.value = combinedState
            }
        }
    }

    // --- Helper Properties ---

    /**
     * Helper property to get the current config data if in success state, null otherwise.
     */
    private val currentConfigData: SettingsConfigData?
        get() = _settingsConfigState.value.dataOrNull

    // --- Public Action Functions ---

    /**
     * Loads all configured LLM models to populate the model selection dropdown.
     *
     * With the repository pattern, this method triggers loading in the model repository.
     * The reactive data streams in the init block will automatically update the UI state.
     */
    fun loadModels() {
        viewModelScope.launch(uiDispatcher) {
            modelRepository.loadModels()
                .fold(
                    ifLeft = { error ->
                        println("Error loading models for settings selection: ${error.message}")
                    },
                    ifRight = {
                        // Success handled by reactive streams
                        // Auto-select the first model if none is selected and we have successful data
                        val currentState = _settingsConfigState.value
                        if (_selectedModel.value == null && currentState is DataState.Success) {
                            val models = currentState.data.models
                            if (models.isNotEmpty()) {
                                selectModel(models.first())
                            }
                        }
                        syncSelectedInstances()
                    }
                )
        }
    }

    /**
     * Selects an LLM model and loads its associated settings profiles.
     */
    fun selectModel(model: LLMModel?) {
        _selectedModel.value = model

        if (model == null) {
            // Clear settings when no model is selected
            // With repository pattern, we don't need to manually update state
            return
        }

        viewModelScope.launch(uiDispatcher) {
            settingsRepository.loadSettingsByModelId(model.id)
                .fold(
                    ifLeft = { error ->
                        println("Error loading settings for model ${model.id}: ${error.message}")
                    },
                    ifRight = { settingsList ->
                        // Filter to only supported types
                        val supportedSettings = settingsList.filter { isModelSettingsSupported(it) }
                        // Repository automatically updates the StateFlow, but we may need to filter
                        // Note: With repository pattern, the filtering should ideally be done in the repository
                        if (supportedSettings.size != settingsList.size) {
                            println("Warning: Found unsupported ModelSettings types for model ${model.id}. Only supported types are displayed.")
                        }
                        syncSelectedInstances()
                    }
                )
        }
    }

    /**
     * Selects a settings profile or clears selection when null.
     */
    fun selectSettings(settings: ModelSettings?) {
        _selectedSettings.value = settings
    }

    /**
     * Initiates the process of adding a new settings profile by showing the form.
     */
    fun startAddingNewSettings() {
        val selectedModel = _selectedModel.value ?: return
        if (selectedModel.type !in getSupportedSettingsTypes()) {
            println("Cannot add new settings: Model type ${selectedModel.type} is not supported.")
            return
        }
        _dialogState.value = SettingsDialogState.AddNewSettings(
            formState = createEmptyNewSettingsForm(selectedModel.type),
        )
    }

    /**
     * Initiates the editing process for an existing settings profile.
     */
    fun startEditingSettings(settings: ModelSettings) {
        if (!isModelSettingsSupported(settings)) {
            println("Cannot edit: Settings with ID ${settings.id} is of unsupported type ${settings::class.simpleName}.")
            return
        }

        _dialogState.value = SettingsDialogState.EditSettings(
            settings = settings,
            formState = settings.toEditFormState()
        )
    }

    /**
     * Initiates the deletion process for a settings profile by showing the confirmation dialog.
     */
    fun startDeletingSettings(settings: ModelSettings) {
        _dialogState.value = SettingsDialogState.DeleteSettings(settings)
    }

    /**
     * Updates any field in the current settings form.
     */
    fun updateSettingsForm(update: (SettingsFormState) -> SettingsFormState) {
        _dialogState.update { dialogState ->
            when (dialogState) {
                is SettingsDialogState.AddNewSettings -> dialogState.copy(
                    formState = update(dialogState.formState)
                )

                is SettingsDialogState.EditSettings -> dialogState.copy(
                    formState = update(dialogState.formState)
                )

                else -> dialogState
            }
        }
    }

    /**
     * Saves the current form - either creates new settings or updates existing ones based on the dialog state.
     */
    fun saveSettings() {
        when (val dialogState = _dialogState.value) {
            is SettingsDialogState.AddNewSettings -> saveNewSettings(dialogState)
            is SettingsDialogState.EditSettings -> saveEditedSettings(dialogState)
            else -> return
        }
    }

    /**
     * Deletes a specific LLM settings profile.
     */
    fun deleteSettings(settingsId: Long) {
        viewModelScope.launch(uiDispatcher) {
            val currentSettings = currentConfigData?.settings
            if (currentSettings == null) {
                println("Cannot delete settings: settings list is not available.")
                return@launch
            }

            settingsRepository.deleteSettings(settingsId)
                .fold(
                    ifLeft = { error ->
                        println("Error deleting settings: ${error.message}")
                    },
                    ifRight = {
                        // Repository automatically updates the StateFlow, no manual list update needed
                        // If the deleted settings was selected, clear the selection
                        if (_selectedSettings.value?.id == settingsId) {
                            _selectedSettings.value = null
                        }
                        cancelDialog()
                    }
                )
        }
    }

    /**
     * Cancels any dialog operation (adding new or editing existing settings).
     */
    fun cancelDialog() {
        _dialogState.value = SettingsDialogState.None
    }

    // --- Private Helper Functions ---

    // Note: updateSettingsInState method removed - repository pattern automatically updates StateFlow

    private fun saveNewSettings(dialogState: SettingsDialogState.AddNewSettings) {
        val form = dialogState.formState
        val validationError = form.validate()
        if (validationError != null) {
            updateSettingsFormError(validationError)
            return
        }
        val modelId = form.modelId ?: return
        val newSettings = form.toModelSettings(0L, modelId)
        viewModelScope.launch(uiDispatcher) {
            settingsRepository.addModelSettings(newSettings)
                .fold(
                    ifLeft = { error ->
                        updateSettingsFormError("Error adding settings: ${error.message}")
                        println("Error adding settings: ${error.message}")
                    },
                    ifRight = { createdSettings ->
                        // Repository automatically updates the StateFlow, no manual list update needed
                        cancelDialog()
                        selectSettings(createdSettings)
                    }
                )
        }
    }

    private fun saveEditedSettings(dialogState: SettingsDialogState.EditSettings) {
        val form = dialogState.formState
        val validationError = form.validate()
        if (validationError != null) {
            updateSettingsFormError(validationError)
            return
        }
        val modelId = form.modelId ?: return

        viewModelScope.launch(uiDispatcher) {
            val originalSettings = dialogState.settings
            val updatedSettings = form.toModelSettings(originalSettings.id, modelId)

            settingsRepository.updateSettings(updatedSettings)
                .fold(
                    ifLeft = { error ->
                        updateSettingsFormError("Error updating settings: ${error.message}")
                        println("Error updating settings: ${error.message}")
                    },
                    ifRight = {
                        // Repository automatically updates the StateFlow, no manual list update needed
                        // Sync the selected settings instance if it's the one updated
                        if (_selectedSettings.value?.id == updatedSettings.id) {
                            _selectedSettings.value = updatedSettings
                        }
                        cancelDialog()
                    }
                )
        }
    }

    private fun updateSettingsFormError(errorMessage: String?) {
        _dialogState.update { dialogState ->
            when (dialogState) {
                is SettingsDialogState.AddNewSettings -> dialogState.copy(
                    formState = dialogState.formState.withError(errorMessage)
                )

                is SettingsDialogState.EditSettings -> dialogState.copy(
                    formState = dialogState.formState.withError(errorMessage)
                )

                else -> dialogState
            }
        }
    }

    /**
     * Ensures that the currently selected settings and model instances are synchronized with the latest loaded data.
     * This is important after operations that refresh the model or settings list.
     */
    private fun syncSelectedInstances() {
        val settingsList = _settingsConfigState.value.dataOrNull?.settings
        val modelsList = _settingsConfigState.value.dataOrNull?.models
        val selectedSettings = _selectedSettings.value
        val selectedModel = _selectedModel.value
        _selectedSettings.value = settingsList?.find { it.id == selectedSettings?.id }
        _selectedModel.value = modelsList?.find { it.id == selectedModel?.id }
    }
}
