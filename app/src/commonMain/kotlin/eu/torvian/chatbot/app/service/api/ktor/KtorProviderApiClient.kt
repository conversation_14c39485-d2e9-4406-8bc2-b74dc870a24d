package eu.torvian.chatbot.app.service.api.ktor

import arrow.core.Either
import eu.torvian.chatbot.app.service.api.ProviderApi
import eu.torvian.chatbot.app.repository.RepositoryError
import eu.torvian.chatbot.common.api.resources.ProviderResource
import eu.torvian.chatbot.common.api.resources.ProviderResource.ById
import eu.torvian.chatbot.common.api.resources.ProviderResource.ById.Credential
import eu.torvian.chatbot.common.api.resources.ProviderResource.ById.Models
import eu.torvian.chatbot.common.models.AddProviderRequest
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.LLMProvider
import eu.torvian.chatbot.common.models.UpdateProviderCredentialRequest
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.plugins.resources.*
import io.ktor.client.request.*

/**
 * Ktor HttpClient implementation of the [ProviderApi] interface.
 *
 * Uses the configured [HttpClient] and the [RepositoryAwareBaseApiClient.safeApiCall] helper
 * to interact with the backend's provider endpoints, mapping responses
 * to [Either<RepositoryError, T>].
 *
 * @property client The Ktor HttpClient instance injected for making requests.
 */
class KtorProviderApiClient(client: HttpClient) : RepositoryAwareBaseApiClient(client), ProviderApi {

    override suspend fun getAllProviders(): Either<RepositoryError, List<LLMProvider>> {
        return safeApiCall {
            client.get(ProviderResource()).body<List<LLMProvider>>()
        }
    }

    override suspend fun addProvider(request: AddProviderRequest): Either<RepositoryError, LLMProvider> {
        return safeApiCall {
            client.post(ProviderResource()) {
                setBody(request)
            }.body<LLMProvider>()
        }
    }

    override suspend fun getProviderById(providerId: Long): Either<RepositoryError, LLMProvider> {
        return safeApiCall {
            client.get(ById(providerId = providerId)).body<LLMProvider>()
        }
    }

    override suspend fun updateProvider(provider: LLMProvider): Either<RepositoryError, Unit> {
        return safeApiCall {
            // Note: OpenAPI specifies the body is the full LLMProvider object,
            // and the path ID must match the body ID.
            // The backend service should handle updating only the allowed fields.
            client.put(ById(providerId = provider.id)) {
                setBody(provider)
            }.body<Unit>()
        }
    }

    override suspend fun deleteProvider(providerId: Long): Either<RepositoryError, Unit> {
        return safeApiCall {
            client.delete(ById(providerId = providerId)).body<Unit>()
        }
    }

    override suspend fun updateProviderCredential(
        providerId: Long,
        request: UpdateProviderCredentialRequest
    ): Either<RepositoryError, Unit> {
        return safeApiCall {
            client.put(Credential(ById(providerId = providerId))) {
                setBody(request)
            }.body<Unit>()
        }
    }

    override suspend fun getModelsByProviderId(providerId: Long): Either<RepositoryError, List<LLMModel>> {
        return safeApiCall {
            client.get(Models(ById(providerId = providerId))).body<List<LLMModel>>()
        }
    }
}