package eu.torvian.chatbot.app.repository

import arrow.core.Either
import arrow.core.right
import eu.torvian.chatbot.app.domain.contracts.DataState
import eu.torvian.chatbot.app.service.api.ProviderApi
import eu.torvian.chatbot.common.models.AddProviderRequest
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.LLMProvider
import eu.torvian.chatbot.common.models.UpdateProviderCredentialRequest
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.withContext

/**
 * Default implementation of [ProviderRepository] that manages LLM provider configurations.
 *
 * This repository maintains an internal cache of provider data using [MutableStateFlow] and
 * provides reactive updates to all observers. It delegates API operations to the injected
 * [ProviderApi] and handles comprehensive error management through [RepositoryError].
 *
 * The repository ensures data consistency by automatically updating the internal StateFlow
 * whenever successful CRUD operations occur, eliminating the need for manual cache invalidation.
 *
 * @property providerApi The API client for provider-related operations
 * @property ioDispatcher The dispatcher for I/O operations
 */
class DefaultProviderRepository(
    private val providerApi: ProviderApi,
    private val ioDispatcher: CoroutineDispatcher = eu.torvian.chatbot.app.utils.misc.ioDispatcher
) : ProviderRepository {

    private val _providers = MutableStateFlow<DataState<RepositoryError, List<LLMProvider>>>(DataState.Idle)
    override val providers: StateFlow<DataState<RepositoryError, List<LLMProvider>>> = _providers.asStateFlow()

    override suspend fun loadProviders(): Either<RepositoryError, Unit> {
        return withContext(ioDispatcher) {
            // Prevent duplicate loading operations
            if (_providers.value.isLoading) return@withContext Unit.right()
            
            _providers.update { DataState.Loading }
            
            providerApi.getAllProviders()
                .map { providerList ->
                    _providers.update { DataState.Success(providerList) }
                }
                .mapLeft { error ->
                    _providers.update { DataState.Error(error) }
                    error
                }
        }
    }

    override suspend fun addProvider(request: AddProviderRequest): Either<RepositoryError, LLMProvider> {
        return withContext(ioDispatcher) {
            providerApi.addProvider(request)
                .map { newProvider ->
                    // Update the StateFlow with the new provider
                    _providers.update { currentState ->
                        when (currentState) {
                            is DataState.Success -> {
                                val updatedList = currentState.data + newProvider
                                DataState.Success(updatedList)
                            }
                            else -> currentState // Keep current state if not in success state
                        }
                    }
                    newProvider
                }
        }
    }

    override suspend fun getProviderById(providerId: Long): Either<RepositoryError, LLMProvider> {
        return withContext(ioDispatcher) {
            providerApi.getProviderById(providerId)
        }
    }

    override suspend fun updateProvider(provider: LLMProvider): Either<RepositoryError, Unit> {
        return withContext(ioDispatcher) {
            providerApi.updateProvider(provider)
                .map {
                    // Update the StateFlow with the modified provider
                    _providers.update { currentState ->
                        when (currentState) {
                            is DataState.Success -> {
                                val updatedList = currentState.data.map { existingProvider ->
                                    if (existingProvider.id == provider.id) provider else existingProvider
                                }
                                DataState.Success(updatedList)
                            }
                            else -> currentState // Keep current state if not in success state
                        }
                    }
                }
        }
    }

    override suspend fun deleteProvider(providerId: Long): Either<RepositoryError, Unit> {
        return withContext(ioDispatcher) {
            providerApi.deleteProvider(providerId)
                .map {
                    // Remove the provider from the StateFlow
                    _providers.update { currentState ->
                        when (currentState) {
                            is DataState.Success -> {
                                val updatedList = currentState.data.filter { it.id != providerId }
                                DataState.Success(updatedList)
                            }
                            else -> currentState // Keep current state if not in success state
                        }
                    }
                }
        }
    }

    override suspend fun updateProviderCredential(
        providerId: Long,
        request: UpdateProviderCredentialRequest
    ): Either<RepositoryError, Unit> {
        return withContext(ioDispatcher) {
            providerApi.updateProviderCredential(providerId, request)
            // Note: Credential updates don't affect the provider metadata in the StateFlow
            // The provider list remains unchanged as credentials are stored separately
        }
    }

    override suspend fun getModelsByProviderId(providerId: Long): Either<RepositoryError, List<LLMModel>> {
        return withContext(ioDispatcher) {
            providerApi.getModelsByProviderId(providerId)
        }
    }
}
