package eu.torvian.chatbot.app.repository

import arrow.core.Either
import arrow.core.right
import eu.torvian.chatbot.app.domain.contracts.DataState
import eu.torvian.chatbot.app.service.api.SettingsApi
import eu.torvian.chatbot.common.models.ModelSettings
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.withContext

/**
 * Default implementation of [SettingsRepository] that manages model settings profiles.
 *
 * This repository maintains an internal cache of settings data using [MutableStateFlow] and
 * provides reactive updates to all observers. It delegates API operations to the injected
 * [SettingsApi] and handles comprehensive error management through [RepositoryError].
 *
 * The repository ensures data consistency by automatically updating the internal StateFlow
 * whenever successful CRUD operations occur, eliminating the need for manual cache invalidation.
 *
 * @property settingsApi The API client for settings-related operations
 * @property ioDispatcher The dispatcher for I/O operations
 */
class DefaultSettingsRepository(
    private val settingsApi: SettingsApi,
    private val ioDispatcher: CoroutineDispatcher = eu.torvian.chatbot.app.utils.misc.ioDispatcher
) : SettingsRepository {

    private val _settings = MutableStateFlow<DataState<RepositoryError, List<ModelSettings>>>(DataState.Idle)
    override val settings: StateFlow<DataState<RepositoryError, List<ModelSettings>>> = _settings.asStateFlow()

    override suspend fun loadSettings(): Either<RepositoryError, Unit> {
        return withContext(ioDispatcher) {
            // Prevent duplicate loading operations
            if (_settings.value.isLoading) return@withContext Unit.right()
            
            _settings.update { DataState.Loading }
            
            // Note: Since SettingsApi doesn't have a getAllSettings method,
            // we'll need to implement this differently or modify the approach
            // For now, we'll return success with empty list and rely on loadSettingsByModelId
            _settings.update { DataState.Success(emptyList()) }
            Unit.right()
        }
    }

    override suspend fun loadSettingsByModelId(modelId: Long): Either<RepositoryError, List<ModelSettings>> {
        return withContext(ioDispatcher) {
            settingsApi.getSettingsByModelId(modelId)
                .map { settingsList ->
                    // Update the StateFlow by merging with existing settings
                    _settings.update { currentState ->
                        when (currentState) {
                            is DataState.Success -> {
                                // Remove existing settings for this model and add the new ones
                                val filteredSettings = currentState.data.filter { it.modelId != modelId }
                                val updatedList = filteredSettings + settingsList
                                DataState.Success(updatedList)
                            }
                            else -> DataState.Success(settingsList)
                        }
                    }
                    settingsList
                }
                .mapLeft { error ->
                    _settings.update { DataState.Error(error) }
                    error
                }
        }
    }

    override suspend fun addModelSettings(settings: ModelSettings): Either<RepositoryError, ModelSettings> {
        return withContext(ioDispatcher) {
            settingsApi.addModelSettings(settings)
                .map { newSettings ->
                    // Update the StateFlow with the new settings
                    _settings.update { currentState ->
                        when (currentState) {
                            is DataState.Success -> {
                                val updatedList = currentState.data + newSettings
                                DataState.Success(updatedList)
                            }
                            else -> currentState // Keep current state if not in success state
                        }
                    }
                    newSettings
                }
        }
    }

    override suspend fun getSettingsById(settingsId: Long): Either<RepositoryError, ModelSettings> {
        return withContext(ioDispatcher) {
            settingsApi.getSettingsById(settingsId)
        }
    }

    override suspend fun updateSettings(settings: ModelSettings): Either<RepositoryError, Unit> {
        return withContext(ioDispatcher) {
            settingsApi.updateSettings(settings)
                .map {
                    // Update the StateFlow with the modified settings
                    _settings.update { currentState ->
                        when (currentState) {
                            is DataState.Success -> {
                                val updatedList = currentState.data.map { existingSettings ->
                                    if (existingSettings.id == settings.id) settings else existingSettings
                                }
                                DataState.Success(updatedList)
                            }
                            else -> currentState // Keep current state if not in success state
                        }
                    }
                }
        }
    }

    override suspend fun deleteSettings(settingsId: Long): Either<RepositoryError, Unit> {
        return withContext(ioDispatcher) {
            settingsApi.deleteSettings(settingsId)
                .map {
                    // Remove the settings from the StateFlow
                    _settings.update { currentState ->
                        when (currentState) {
                            is DataState.Success -> {
                                val updatedList = currentState.data.filter { it.id != settingsId }
                                DataState.Success(updatedList)
                            }
                            else -> currentState // Keep current state if not in success state
                        }
                    }
                }
        }
    }
}
