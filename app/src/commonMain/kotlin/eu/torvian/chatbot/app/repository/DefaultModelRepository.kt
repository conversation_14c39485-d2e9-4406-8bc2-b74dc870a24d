package eu.torvian.chatbot.app.repository

import arrow.core.Either
import arrow.core.right
import eu.torvian.chatbot.app.domain.contracts.DataState
import eu.torvian.chatbot.app.service.api.ModelApi
import eu.torvian.chatbot.common.models.AddModelRequest
import eu.torvian.chatbot.common.models.ApiKeyStatusResponse
import eu.torvian.chatbot.common.models.LLMModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.withContext

/**
 * Default implementation of [ModelRepository] that manages LLM model configurations.
 *
 * This repository maintains an internal cache of model data using [MutableStateFlow] and
 * provides reactive updates to all observers. It delegates API operations to the injected
 * [ModelApi] and handles comprehensive error management through [RepositoryError].
 *
 * The repository ensures data consistency by automatically updating the internal StateFlow
 * whenever successful CRUD operations occur, eliminating the need for manual cache invalidation.
 *
 * @property modelApi The API client for model-related operations
 * @property ioDispatcher The dispatcher for I/O operations
 */
class DefaultModelRepository(
    private val modelApi: ModelApi,
    private val ioDispatcher: CoroutineDispatcher = eu.torvian.chatbot.app.utils.misc.ioDispatcher
) : ModelRepository {

    private val _models = MutableStateFlow<DataState<RepositoryError, List<LLMModel>>>(DataState.Idle)
    override val models: StateFlow<DataState<RepositoryError, List<LLMModel>>> = _models.asStateFlow()

    override suspend fun loadModels(): Either<RepositoryError, Unit> {
        return withContext(ioDispatcher) {
            // Prevent duplicate loading operations
            if (_models.value.isLoading) return@withContext Unit.right()
            
            _models.update { DataState.Loading }
            
            modelApi.getAllModels()
                .map { modelList ->
                    _models.update { DataState.Success(modelList) }
                }
                .mapLeft { error ->
                    _models.update { DataState.Error(error) }
                    error
                }
        }
    }

    override suspend fun addModel(request: AddModelRequest): Either<RepositoryError, LLMModel> {
        return withContext(ioDispatcher) {
            modelApi.addModel(request)
                .map { newModel ->
                    // Update the StateFlow with the new model
                    _models.update { currentState ->
                        when (currentState) {
                            is DataState.Success -> {
                                val updatedList = currentState.data + newModel
                                DataState.Success(updatedList)
                            }
                            else -> currentState // Keep current state if not in success state
                        }
                    }
                    newModel
                }
        }
    }

    override suspend fun getModelById(modelId: Long): Either<RepositoryError, LLMModel> {
        return withContext(ioDispatcher) {
            modelApi.getModelById(modelId)
        }
    }

    override suspend fun updateModel(model: LLMModel): Either<RepositoryError, Unit> {
        return withContext(ioDispatcher) {
            modelApi.updateModel(model)
                .map {
                    // Update the StateFlow with the modified model
                    _models.update { currentState ->
                        when (currentState) {
                            is DataState.Success -> {
                                val updatedList = currentState.data.map { existingModel ->
                                    if (existingModel.id == model.id) model else existingModel
                                }
                                DataState.Success(updatedList)
                            }
                            else -> currentState // Keep current state if not in success state
                        }
                    }
                }
        }
    }

    override suspend fun deleteModel(modelId: Long): Either<RepositoryError, Unit> {
        return withContext(ioDispatcher) {
            modelApi.deleteModel(modelId)
                .map {
                    // Remove the model from the StateFlow
                    _models.update { currentState ->
                        when (currentState) {
                            is DataState.Success -> {
                                val updatedList = currentState.data.filter { it.id != modelId }
                                DataState.Success(updatedList)
                            }
                            else -> currentState // Keep current state if not in success state
                        }
                    }
                }
        }
    }

    override suspend fun getModelApiKeyStatus(modelId: Long): Either<RepositoryError, ApiKeyStatusResponse> {
        return withContext(ioDispatcher) {
            modelApi.getModelApiKeyStatus(modelId)
        }
    }
}
