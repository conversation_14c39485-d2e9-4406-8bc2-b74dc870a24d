package eu.torvian.chatbot.app.repository

import arrow.core.Either
import eu.torvian.chatbot.app.domain.contracts.DataState
import eu.torvian.chatbot.common.models.AddModelRequest
import eu.torvian.chatbot.common.models.ApiKeyStatusResponse
import eu.torvian.chatbot.common.models.LLMModel
import kotlinx.coroutines.flow.StateFlow

/**
 * Repository interface for managing LLM model configurations.
 *
 * This repository serves as the single source of truth for model data in the application,
 * providing reactive data streams through StateFlow and handling all model-related operations.
 * It abstracts the underlying API layer and provides comprehensive error handling through
 * the RepositoryError hierarchy.
 *
 * The repository maintains an internal cache of model data and automatically updates
 * all observers when changes occur, ensuring data consistency across the application.
 */
interface ModelRepository {
    
    /**
     * Reactive stream of all LLM model configurations.
     * 
     * This StateFlow provides real-time updates whenever the model data changes,
     * allowing ViewModels and other consumers to automatically react to data changes
     * without manual refresh operations.
     *
     * @return StateFlow containing the current state of all models wrapped in DataState
     */
    val models: StateFlow<DataState<RepositoryError, List<LLMModel>>>

    /**
     * Loads all LLM model configurations from the backend.
     *
     * This operation fetches the latest model data and updates the internal StateFlow.
     * If a load operation is already in progress, this method returns immediately
     * without starting a duplicate operation.
     *
     * @return Either.Right with Unit on successful load, or Either.Left with RepositoryError on failure
     */
    suspend fun loadModels(): Either<RepositoryError, Unit>

    /**
     * Adds a new LLM model configuration.
     *
     * Upon successful creation, the new model is automatically added to the internal
     * StateFlow, triggering updates to all observers.
     *
     * @param request The model creation request containing all necessary details
     * @return Either.Right with the created LLMModel on success, or Either.Left with RepositoryError on failure
     */
    suspend fun addModel(request: AddModelRequest): Either<RepositoryError, LLMModel>

    /**
     * Retrieves a specific LLM model configuration by its ID.
     *
     * This method provides direct access to a single model without affecting
     * the main models StateFlow.
     *
     * @param modelId The unique identifier of the model to retrieve
     * @return Either.Right with the LLMModel on success, or Either.Left with RepositoryError on failure
     */
    suspend fun getModelById(modelId: Long): Either<RepositoryError, LLMModel>

    /**
     * Updates an existing LLM model configuration.
     *
     * Upon successful update, the modified model replaces the existing one in the
     * internal StateFlow, triggering updates to all observers.
     *
     * @param model The updated model object with the same ID as the existing model
     * @return Either.Right with Unit on successful update, or Either.Left with RepositoryError on failure
     */
    suspend fun updateModel(model: LLMModel): Either<RepositoryError, Unit>

    /**
     * Deletes an LLM model configuration.
     *
     * Upon successful deletion, the model is automatically removed from the internal
     * StateFlow, triggering updates to all observers.
     *
     * @param modelId The unique identifier of the model to delete
     * @return Either.Right with Unit on successful deletion, or Either.Left with RepositoryError on failure
     */
    suspend fun deleteModel(modelId: Long): Either<RepositoryError, Unit>

    /**
     * Checks the API key status for a specific model's provider.
     *
     * This method provides information about whether the provider associated with
     * the specified model has a valid API key configured.
     *
     * @param modelId The unique identifier of the model whose provider's API key status to check
     * @return Either.Right with ApiKeyStatusResponse on success, or Either.Left with RepositoryError on failure
     */
    suspend fun getModelApiKeyStatus(modelId: Long): Either<RepositoryError, ApiKeyStatusResponse>
}
