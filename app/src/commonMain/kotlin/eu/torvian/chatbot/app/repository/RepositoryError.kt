package eu.torvian.chatbot.app.repository

import eu.torvian.chatbot.common.api.ApiError

/**
 * Comprehensive error hierarchy for the repository layer that provides structured
 * error handling for all possible failures in data access operations.
 *
 * This sealed class replaces the limited scope of [eu.torvian.chatbot.common.api.ApiError] for the new repository
 * architecture, providing clear distinction between different types of errors:
 * - Network connectivity issues
 * - Server-side errors (wrapping existing ApiError)
 * - Data serialization/parsing problems
 * - Unexpected system errors
 *
 * @property message A human-readable description of the error
 * @property cause The underlying throwable that caused this error, if any
 */
sealed class RepositoryError(val message: String, val cause: Throwable? = null) {
    
    /**
     * Represents network connectivity issues such as timeouts, connection failures,
     * or other I/O related problems that occur before reaching the server.
     *
     * @property throwable The underlying network exception
     * @property description Human-readable description of the network error
     */
    data class NetworkError(
        val throwable: Throwable,
        val description: String = throwable.message ?: "Network error occurred."
    ) : RepositoryError(description, throwable)

    /**
     * Wraps the existing [eu.torvian.chatbot.common.api.ApiError] for errors that are actually returned by the backend server.
     * This maintains compatibility with the existing error handling while providing
     * a unified error hierarchy.
     *
     * @property apiError The original API error from the server response
     */
    data class ServerError(
        val apiError: ApiError
    ) : RepositoryError(apiError.message, null)

    /**
     * Represents errors in parsing, serializing, or converting data between formats.
     * This includes JSON deserialization failures, type conversion errors, etc.
     *
     * @property description Human-readable description of the serialization error
     * @property throwable The underlying serialization exception, if available
     */
    data class SerializationError(
        val description: String,
        val throwable: Throwable? = null
    ) : RepositoryError(description, throwable)

    /**
     * Represents any unhandled or unexpected exceptions that don't fit into
     * the other error categories. This serves as a catch-all for system errors.
     *
     * @property description Human-readable description of the unknown error
     * @property throwable The underlying exception, if available
     */
    data class UnknownError(
        val description: String = "An unknown error occurred.",
        val throwable: Throwable? = null
    ) : RepositoryError(description, throwable)
}

/**
 * Extension function to convert an [ApiError] to a [RepositoryError.ServerError].
 * This provides easy conversion from the existing error type to the new hierarchy.
 *
 * @return A [RepositoryError.ServerError] wrapping this [ApiError]
 */
fun ApiError.toRepositoryError(): RepositoryError.ServerError = RepositoryError.ServerError(this)
