# SettingsScreen Refactor Plan

## Overview

Refactor the monolithic `SettingsScreen.kt` into a well-structured package following the same patterns used in the ChatArea refactor. This will improve maintainability, testability, and code organization by applying state hoisting and separation of concerns.

## Goals

1. **State Hoisting**: Remove direct ViewModel dependencies from composables (except the main SettingsScreen)
2. **Separation of Concerns**: Split the large file into focused, single-responsibility components
3. **Consistent Structure**: Follow the same organizational patterns as `compose.chatarea`
4. **Maintainability**: Make each component easier to understand, test, and modify

## New Package Structure

Create new package: `eu.torvian.chatbot.app.compose.settings`

### Files to Create

#### 1. `SettingsScreen.kt` (Main Entry Point)
- **Purpose**: Main stateful wrapper, only component that directly uses ViewModels
- **Responsibilities**:
  - Manage ViewModels (ProviderConfigViewModel, ModelConfigViewModel, SettingsConfigViewModel)
  - Handle tab state and navigation
  - Collect states from ViewModels and pass to child components
  - Convert ViewModel actions to callback functions

#### 2. `SettingsState.kt` (State Contracts)
- **Purpose**: Define state data classes and contracts
- **Contents**:
  - `SettingsTabState` - overall tab state
  - `ProvidersTabState` - providers tab state contract
  - `ModelsTabState` - models tab state contract  
  - `SettingsConfigTabState` - settings config tab state contract
  - `ProviderFormState` - form state for add/edit provider dialogs

#### 3. `SettingsActions.kt` (Action Contracts)
- **Purpose**: Define action callback interfaces
- **Contents**:
  - `SettingsActions` - main actions interface
  - `ProvidersTabActions` - providers-specific actions
  - `ModelsTabActions` - models-specific actions
  - `SettingsConfigTabActions` - settings config-specific actions

#### 4. `ProvidersTab.kt` (Providers Management)
- **Purpose**: Stateless providers tab component
- **Responsibilities**:
  - Display providers list and detail panels
  - Handle provider selection state locally
  - Show add/edit/delete dialogs
  - Use actions callbacks instead of direct ViewModel calls

#### 5. `ProvidersListPanel.kt` (Provider List)
- **Purpose**: Master panel showing list of providers
- **Responsibilities**:
  - Display providers in a selectable list
  - Handle selection highlighting
  - Show empty state when no providers exist

#### 6. `ProviderDetailPanel.kt` (Provider Details)
- **Purpose**: Detail panel for selected provider
- **Responsibilities**:
  - Display provider information
  - Show action buttons (edit, delete, manage credentials)
  - Handle "no selection" state

#### 7. `ProviderListItem.kt` (Individual Provider Item)
- **Purpose**: Individual provider item in the list
- **Responsibilities**:
  - Display provider summary information
  - Handle selection state styling
  - Show API key status indicator

#### 8. `ProviderDialogs.kt` (Provider Dialogs)
- **Purpose**: All provider-related dialogs
- **Contents**:
  - `AddProviderDialog` - dialog for adding new provider
  - `EditProviderDialog` - dialog for editing existing provider  
  - `DeleteProviderConfirmationDialog` - confirmation dialog for deletion
- **Note**: Use action callbacks instead of direct ViewModel calls

#### 9. `ModelsTab.kt` (Models Management)
- **Purpose**: Stateless models tab component
- **Responsibilities**:
  - Display models list and management UI
  - Use actions callbacks instead of direct ViewModel calls
  - Prepare for future Phase 3 implementation

#### 10. `SettingsConfigTab.kt` (Settings Management)
- **Purpose**: Stateless settings config tab component
- **Responsibilities**:
  - Display model settings management UI
  - Use actions callbacks instead of direct ViewModel calls
  - Prepare for future Phase 4 implementation

#### 11. `ProviderDetailsSection.kt` (Provider Details Display)
- **Purpose**: Reusable component for displaying provider details
- **Responsibilities**:
  - Show structured provider information
  - Display API key status
  - Use consistent formatting

#### 12. `DetailRow.kt` (Label-Value Display)
- **Purpose**: Reusable component for label-value pairs
- **Responsibilities**:
  - Consistent formatting for detail displays
  - Reusable across different detail sections

## Detailed Refactoring Steps

### Step 1: Create State Contracts (`SettingsState.kt`)

```kotlin
// State contracts that replace direct ViewModel state access
data class ProvidersTabState(
    val providersUiState: UiState<List<LLMProvider>>,
    val isAddingNewProvider: Boolean,
    val editingProvider: LLMProvider?,
    val newProviderForm: ProviderFormState,
    val editingProviderForm: ProviderFormState,
    val credentialUpdateLoading: Boolean
)

data class ModelsTabState(
    val modelsUiState: UiState<List<LLMModel>>
)

data class SettingsConfigTabState(
    val modelsForSelection: UiState<List<LLMModel>>,
    val selectedModelId: Long?,
    val settingsState: UiState<List<ModelSettingsProfile>>
)

// Form states extracted from ViewModel
data class ProviderFormState(
    val name: String,
    val type: LLMProviderType,
    val baseUrl: String,
    val description: String,
    val credential: String,
    val newCredentialInput: String,
    val errorMessage: String?
)
```

### Step 2: Create Action Contracts (`SettingsActions.kt`)

```kotlin
// Action interfaces that replace direct ViewModel method calls
interface ProvidersTabActions {
    fun onLoadProviders()
    fun onStartAddingNewProvider()
    fun onCancelAddingNewProvider()
    fun onAddNewProvider()
    fun onStartEditingProvider(provider: LLMProvider)
    fun onCancelEditingProvider()
    fun onUpdateProvider()
    fun onDeleteProvider(providerId: Long)
    fun onUpdateProviderCredential()
    
    // Form field updates
    fun onUpdateNewProviderName(name: String)
    fun onUpdateNewProviderType(type: LLMProviderType)
    fun onUpdateNewProviderBaseUrl(baseUrl: String)
    fun onUpdateNewProviderDescription(description: String)
    fun onUpdateNewProviderCredential(credential: String)
    
    // Edit form field updates
    fun onUpdateEditingProviderName(name: String)
    fun onUpdateEditingProviderType(type: LLMProviderType)
    fun onUpdateEditingProviderBaseUrl(baseUrl: String)
    fun onUpdateEditingProviderDescription(description: String)
    fun onUpdateEditingProviderNewCredentialInput(credential: String)
}

interface ModelsTabActions {
    fun onLoadModelsAndProviders()
}

interface SettingsConfigTabActions {
    fun onLoadModels()
}
```

### Step 3: Refactor Main SettingsScreen

Transform the main SettingsScreen to:
- Only handle ViewModels directly
- Collect all states and pass to child components
- Create action implementations that delegate to ViewModels
- Manage tab navigation state

### Step 4: Extract Tab Components

Create separate stateless components for each tab:
- `ProvidersTab` - handles providers management
- `ModelsTab` - handles models management  
- `SettingsConfigTab` - handles settings configuration

### Step 5: Extract Provider-Specific Components

Break down provider management into focused components:
- `ProvidersListPanel` - master list
- `ProviderDetailPanel` - detail view
- `ProviderListItem` - individual list items
- `ProviderDetailsSection` - structured details display

### Step 6: Extract Dialog Components

Move all dialogs to `ProviderDialogs.kt`:
- `AddProviderDialog`
- `EditProviderDialog`  
- `DeleteProviderConfirmationDialog`

### Step 7: Extract Utility Components

Create reusable utility components:
- `DetailRow` - for consistent label-value display
- Any other reusable UI components

## Migration Benefits

### Before (Current State)
- Single 800+ line file with multiple concerns
- Tight coupling between UI and ViewModels
- Difficult to test individual components
- Hard to understand and maintain

### After (Refactored State)
- 12 focused files with single responsibilities
- State hoisting with clear contracts
- Testable stateless components
- Clear separation of concerns
- Follows established patterns from ChatArea

## State Hoisting Strategy

### ViewModel Interaction
- **Only SettingsScreen.kt** directly uses ViewModels
- All other components receive state via props
- All actions are callback functions passed down

### Local State Management
- Components manage their own local UI state (e.g., selected provider)
- Global state comes from ViewModels via state hoisting
- Clear boundaries between local and global state

### Action Flow
1. User interaction triggers callback in component
2. Callback flows up to SettingsScreen  
3. SettingsScreen calls appropriate ViewModel method
4. ViewModel updates state
5. State flows back down to components

## Implementation Notes

### Consistency with ChatArea Pattern
- Follow same file naming conventions
- Use same state/action separation pattern
- Apply same level of component granularity
- Maintain similar code organization

### Backward Compatibility  
- Keep same public API for SettingsScreen composable
- Maintain same functionality and behavior
- No breaking changes to existing integrations

### Future Extensibility
- Prepared for Phase 3 (Models) and Phase 4 (Settings Config) implementations
- Clear extension points for new features
- Modular structure supports incremental development

## Testing Strategy

### Component Testing
- Each stateless component can be tested in isolation
- State contracts make it easy to create test states
- Action callbacks can be mocked for testing

### Integration Testing  
- Test state flow from ViewModels to components
- Verify action callback implementations
- Test tab navigation and state management

## File Size Expectations

- **SettingsScreen.kt**: ~150-200 lines (main coordinator)
- **ProvidersTab.kt**: ~100-150 lines (tab component)
- **ProviderDialogs.kt**: ~300-400 lines (all dialogs)
- **ProvidersListPanel.kt**: ~80-100 lines (list component)
- **ProviderDetailPanel.kt**: ~100-120 lines (detail component)
- **Other components**: 50-100 lines each

Total: Similar line count but much better organized and maintainable.
