Okay team, let's look at an important architectural upgrade designed to make our application's data handling more robust, reliable, and easier to maintain. This document outlines a plan to refactor how our ViewModels interact with data, focusing initially on the Model, Provider, and Settings configuration features.

---

## Architectural Refactoring: Implementing the Repository Pattern with Reactive Data Streams

### Why We're Doing This: Addressing Current Challenges

Our current application architecture, where our UI components (Viewmodels) directly call API services, works, but it's starting to show some common challenges as the application grows:

1.  **Stale Data and Lack of Synchronization:**
    *   Imagine two different screens or ViewModels that both need a list of LLM Providers. If one ViewModel adds a new provider, the other ViewModels won't automatically know about this change. They'll continue displaying old (stale) data until they explicitly refresh it. This can lead to user confusion and requires developers to manually manage data synchronization, which is error-prone.

2.  **Limited and Inconsistent Error Handling:**
    *   Our current `ApiError` structure is designed to represent specific error responses *from our backend API* (e.g., HTTP 400 Bad Request with a detailed message). However, it struggles to elegantly handle issues that occur *before* we even get a response from the server, such as:
        *   The user having no internet connection.
        *   A network timeout.
        *   The server being unreachable.
        *   Errors in parsing the data we receive.
    *   These issues are currently mapped to generic `ApiError` types, which doesn't provide clear, actionable feedback for logging or for the user.

3.  **Tight Coupling:**
    *   Our ViewModels are tightly coupled to the specifics of how data is fetched (i.e., directly to the `Api` client implementations). This makes it harder to change how data is sourced (e.g., adding local caching, supporting offline mode) without modifying many ViewModels.

### The Solution: Introducing a Repository Layer with Reactive Data Streams

To address these challenges, we're adopting a well-established architectural pattern: the **Repository Pattern with Reactive Data Streams**.

This pattern involves:

*   **A New Layer: Repositories:** ViewModels will no longer talk directly to API clients. Instead, they will communicate with new **Repository** classes. Each repository will be responsible for a specific type of data (e.g., `ProviderRepository` for `LLMProvider`s, `ModelRepository` for `LLMModel`s).
*   **Single Source of Truth:** Each repository will be the definitive owner of its data. It will manage how data is fetched (from APIs), how it's stored (temporarily in memory), and how it's updated.
*   **Reactive Data Streams (`StateFlow`):** Repositories will expose their data using Kotlin `StateFlow`s. ViewModels will "observe" these flows. This means whenever the data in a repository changes (e.g., a new provider is added), all observing ViewModels are *automatically and immediately notified* with the latest data. No more manual refreshing!
*   **Robust Error Handling (`RepositoryError`):** We're introducing a new, more comprehensive error type called `RepositoryError`. This allows us to clearly distinguish between network issues, server errors, data parsing problems, and other unexpected failures at the data layer.
*   **Decoupling:** ViewModels will be blissfully unaware of whether data comes from an API, a database, or a cache. They simply ask the repository for data and observe its state.

### Phased Migration Strategy: Why We're Doing It This Way

Implementing this change across the entire application at once would be a massive undertaking. To manage risk and allow for incremental development, we're adopting a **phased migration strategy**:

*   **Targeted Refactoring:** Initially, we will apply this new architecture only to the `Model`, `Provider`, and `Settings` configuration features.
*   **Coexisting Components:**
    *   The existing `BaseApiClient` and its associated `Either<ApiError, T>` return type will remain **unchanged** for all other (non-migrated) API clients.
    *   The existing `UiState<ApiError, T>` class will also remain **unchanged** and continue to be used by all other (non-migrated) ViewModels.
*   **New Components for Migrated Features:** We will introduce *new* classes to support the migrated features:
    *   `RepositoryAwareBaseApiClient`: A new base API client that specifically handles `RepositoryError`.
    *   `DataState`: A new state-tracking class that uses `RepositoryError`.
    *   New `Repository` interfaces and their default implementations.

This approach allows us to gradually roll out the new architecture, minimize the impact on existing, stable code, and provide immediate benefits to the targeted features.

---

### Core Architectural Changes & Implementation Steps

Here's a detailed breakdown of the required code changes for the `Model`, `Provider`, and `Settings` features:

#### 1. New Error Hierarchy: `RepositoryError`

*   **Purpose:** To provide a comprehensive and structured way to represent all possible errors originating from the data access layer. This replaces the limited scope of `ApiError` for our new architecture.
*   **Changes:**
    *   **Create New File:** `common/src/commonMain/kotlin/eu/torvian/chatbot/common/api/RepositoryError.kt`
    *   **Define `RepositoryError` sealed class:** This will have specific subclasses:
        *   `RepositoryError.NetworkError`: For network connectivity issues (e.g., `IOException`).
        *   `RepositoryError.ServerError`: To wrap the existing `ApiError` (for errors actually returned by the backend).
        *   `RepositoryError.SerializationError`: For issues in parsing or converting data.
        *   `RepositoryError.UnknownError`: For any unhandled or unexpected exceptions.
    *   **Add Extension Function:** `fun ApiError.toRepositoryError(): RepositoryError.ServerError` for easy conversion.
*   **Impact:** This new error type will be used throughout the new API client, repository, and ViewModel layers for the migrated features. The existing `ApiError` will still exist and be wrapped by `RepositoryError.ServerError`.

**Code Example (`common/src/commonMain/kotlin/eu/torvian/chatbot/common/api/RepositoryError.kt`)**

```kotlin
// New file: common/src/commonMain/kotlin/eu/torvian/chatbot/common/api/RepositoryError.kt
package eu.torvian.chatbot.common.api

sealed class RepositoryError(val message: String, val cause: Throwable? = null) {
    data class NetworkError(
        val throwable: Throwable,
        val description: String = throwable.message ?: "Network error occurred."
    ) : RepositoryError(description, throwable)

    data class ServerError(
        val apiError: ApiError // Still references the existing ApiError
    ) : RepositoryError(apiError.message, null)

    data class SerializationError(
        val description: String,
        val throwable: Throwable? = null
    ) : RepositoryError(description, throwable)

    data class UnknownError(
        val description: String = "An unknown error occurred.",
        val throwable: Throwable? = null
    ) : RepositoryError(description, throwable)
}

fun ApiError.toRepositoryError(): RepositoryError.ServerError = RepositoryError.ServerError(this)
```

#### 2. New Data State for Migrated Features: `DataState`

*   **Purpose:** To represent the complete lifecycle of a data resource (idle, loading, success, error) for the migrated features. This is a domain-agnostic equivalent of `UiState`, but using `RepositoryError`.
*   **Changes:**
    *   **Create New File:** `app/src/commonMain/kotlin/eu/torvian/chatbot/app/domain/contracts/DataState.kt`
    *   **Define `DataState` sealed class:** This will have `Idle`, `Loading`, `Success<T>`, and `Error<RepositoryError>` states.
    *   **Important:** The existing `UiState.kt` file and class will **remain untouched**.
*   **Impact:** This new `DataState` class will be the type used in `StateFlow`s exposed by the Repositories and observed by the migrated ViewModels.

**Code Example (`app/src/commonMain/kotlin/eu/torvian/chatbot/app/domain/contracts/DataState.kt`)**

```kotlin
// New file: app/src/commonMain/kotlin/eu/torvian/chatbot/app/domain/contracts/DataState.kt
package eu.torvian.chatbot.app.domain.contracts

import eu.torvian.chatbot.common.api.RepositoryError // Imports RepositoryError

sealed class DataState<out E, out T> {
    object Idle : DataState<Nothing, Nothing>()
    object Loading : DataState<Nothing, Nothing>()
    data class Success<out T>(val data: T) : DataState<Nothing, T>()
    data class Error<out E>(val error: E) : DataState<E, Nothing>()
    val isLoading: Boolean get() = this is Loading
    val isSuccess: Boolean get() = this is Success
    val isError: Boolean get() = this is Error
    val isIdle: Boolean get() = this is Idle
    val dataOrNull: T? get() = (this as? Success)?.data
    val errorOrNull: E? get() = (this as? Error)?.error
}
```

#### 3. New Base API Client for Migrated Features: `RepositoryAwareBaseApiClient`

*   **Purpose:** To provide a `safeApiCall` helper that specifically translates network and API responses into our new `RepositoryError` types for the migrated features, without altering the existing `BaseApiClient`.
*   **Changes:**
    *   **Create New File:** `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/RepositoryAwareBaseApiClient.kt`
    *   **Define `RepositoryAwareBaseApiClient` abstract class:** It will take an `HttpClient` and contain a `protected suspend fun <T> safeApiCall(...)` with the new comprehensive error handling.
    *   **Important:** The existing `BaseApiClient.kt` file and class will **remain untouched**.
*   **Impact:** `KtorModelApiClient`, `KtorProviderApiClient.kt`, and `KtorSettingsApiClient.kt` will now inherit from *this new class*.

**Code Example (`app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/RepositoryAwareBaseApiClient.kt`)**

```kotlin
// New file: app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/RepositoryAwareBaseApiClient.kt
package eu.torvian.chatbot.app.service.api.ktor

import arrow.core.Either
import arrow.core.left
import eu.torvian.chatbot.app.utils.misc.KmpLogger
import eu.torvian.chatbot.app.utils.misc.ioDispatcher
import eu.torvian.chatbot.app.utils.misc.kmpLogger
import eu.torvian.chatbot.common.api.ApiError
import eu.torvian.chatbot.common.api.RepositoryError
import eu.torvian.chatbot.common.api.toRepositoryError
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.plugins.*
import io.ktor.client.statement.*
import io.ktor.serialization.*
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.withContext
import java.io.IOException

abstract class RepositoryAwareBaseApiClient(protected val client: HttpClient) {
    companion object {
        private val logger: KmpLogger = kmpLogger<RepositoryAwareBaseApiClient>()
    }

    protected suspend fun <T> safeApiCall(
        block: suspend () -> T
    ): Either<RepositoryError, T> {
        return withContext(ioDispatcher) {
            try {
                Either.Right(block())
            } catch (e: ClientRequestException) {
                logger.warn("ClientRequestException: Status ${e.response.status}, Body: ${e.response.bodyAsText()}")
                tryDeserializeApiError(e.response, e)
            } catch (e: ServerResponseException) {
                logger.warn("ServerResponseException: Status ${e.response.status}, Body: ${e.response.bodyAsText()}")
                tryDeserializeApiError(e.response, e)
            } catch (e: RedirectResponseException) {
                logger.warn("RedirectResponseException: Status ${e.response.status}")
                tryDeserializeApiError(e.response, e)
            } catch (e: ResponseException) {
                logger.warn("Unexpected ResponseException: Status ${e.response.status}, Body: ${e.response.bodyAsText()}")
                RepositoryError.UnknownError("HTTP Response Error: ${e.message}", e).left()
            } catch (e: IOException) {
                logger.warn("IOException (Network Error): ${e.message}")
                RepositoryError.NetworkError(e).left()
            } catch (e: ContentConvertException) {
                logger.warn("ContentConvertException (Serialization Error): ${e.message}")
                RepositoryError.SerializationError("Data Serialization/Deserialization Error: ${e.message}", e).left()
            } catch (e: CancellationException) {
                logger.warn("Request cancelled: ${e.message}")
                throw e
            } catch (e: Exception) {
                logger.error("Unexpected Exception during API call: ${e.message}", e)
                RepositoryError.UnknownError(e.message ?: "An unexpected client error occurred", e).left()
            }
        }
    }

    private suspend fun <T> tryDeserializeApiError(
        response: HttpResponse,
        originalException: ResponseException
    ): Either<RepositoryError, T> {
        return try {
            val apiError = response.body<ApiError>()
            apiError.toRepositoryError().left()
        } catch (e: ContentConvertException) {
            logger.warn("ContentConvertException reading error body for status ${response.status.value}: ${e.message}")
            RepositoryError.SerializationError("Failed to parse server error response: ${e.message}", e).left()
        } catch (e: Exception) {
            logger.warn("Unexpected exception reading error body for status ${response.status.value}: ${e.message}")
            RepositoryError.UnknownError("Failed to process server error response: ${e.message}", e).left()
        }
    }
}
```

#### 4. API Layer Modifications for Migrated Features

*   **Purpose:** To update the API interfaces and their Ktor implementations for `Model`, `Provider`, and `Settings` to use the new `RepositoryError`.
*   **Changes:**
    *   **API Interfaces (`ModelApi`, `ProviderApi`, `SettingsApi`):**
        *   Modify `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ModelApi.kt`, `ProviderApi.kt`, and `SettingsApi.kt`.
        *   Change the `Either.Left` type in all suspend function signatures from `ApiError` to `RepositoryError`.
    *   **Ktor API Client Implementations (`KtorModelApiClient`, `KtorProviderApiClient`, `KtorSettingsApiClient`):**
        *   Modify `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorModelApiClient.kt`, `KtorProviderApiClient.kt`, and `KtorSettingsApiClient.kt`.
        *   Change their base class from `BaseApiClient` to `RepositoryAwareBaseApiClient`.
        *   Ensure they no longer import `ApiError` directly from `common.api` (as it's wrapped by `RepositoryError.ServerError`).
*   **Impact:** These API clients will now return `Either<RepositoryError, T>`, using the enhanced error handling from `RepositoryAwareBaseApiClient`. Other API clients (not migrated) remain unchanged.

**Code Example (Modified `app/src/commonMain/kotlin/eu.torvian/chatbot/app/service/api/ModelApi.kt`)**

```kotlin
package eu.torvian.chatbot.app.service.api
import arrow.core.Either
import eu.torvian.chatbot.common.api.RepositoryError // Now imports RepositoryError
// ... other imports

interface ModelApi {
    suspend fun getAllModels(): Either<RepositoryError, List<LLMModel>> // Changed from ApiError
    suspend fun addModel(request: AddModelRequest): Either<RepositoryError, LLMModel> // Changed from ApiError
    // ... all other methods updated similarly
}
```

**Code Example (Modified `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorModelApiClient.kt`)**

```kotlin
package eu.torvian.chatbot.app.service.api.ktor
import arrow.core.Either
import eu.torvian.chatbot.app.service.api.ModelApi
import eu.torvian.chatbot.common.api.RepositoryError // Imports RepositoryError
// ... other imports
import io.ktor.client.*

class KtorModelApiClient(client: HttpClient) : RepositoryAwareBaseApiClient(client), ModelApi { // Changed base class
    override suspend fun getAllModels(): Either<RepositoryError, List<LLMModel>> { // Return type changed
        return safeApiCall { // This now calls RepositoryAwareBaseApiClient's safeApiCall
            client.get(ModelResource()).body<List<LLMModel>>()
        }
    }
    // ... all other methods updated similarly
}
```
*(Apply identical changes to `KtorProviderApiClient.kt` and `KtorSettingsApiClient.kt`)*

#### 5. New Repository Layer

*   **Purpose:** To establish the Single Source of Truth for the `Model`, `Provider`, and `Settings` data, managing data fetching, in-memory caching, and reactive updates.
*   **Changes:**
    *   **Create New Interfaces:** `app/src/commonMain/kotlin/eu/torvian/chatbot/app/repository/ModelRepository.kt`, `ProviderRepository.kt`, `SettingsRepository.kt`.
    *   **Create Default Implementations:** `app/src/commonMain/kotlin/eu/torvian/chatbot/app/repository/DefaultModelRepository.kt`, `DefaultProviderRepository.kt`, `DefaultSettingsRepository.kt`.
    *   **Structure:**
        *   Each repository will maintain an internal `MutableStateFlow<DataState<RepositoryError, List<T>>>` (e.g., `_providers`, `_models`).
        *   Public methods will be suspend functions (e.g., `loadProviders()`, `addProvider(request)`) that delegate to the *newly modified* `Api` client.
        *   Upon successful API calls, the repository **must update its internal `MutableStateFlow`** to push changes to all observers.
*   **Impact:** This layer centralizes data logic and provides reactive data streams to ViewModels, ensuring synchronization.

**Code Example (New `app/src/commonMain/kotlin/eu/torvian/chatbot/app/repository/ProviderRepository.kt` interface and partial implementation)**

```kotlin
// New file: app/src/commonMain/kotlin/eu/torvian/chatbot/app/repository/ProviderRepository.kt
package eu.torvian.chatbot.app.repository
import arrow.core.Either
import eu.torvian.chatbot.app.domain.contracts.DataState // Now imports DataState
import eu.torvian.chatbot.app.service.api.ProviderApi // Inject the new Api interface
import eu.torvian.chatbot.common.api.RepositoryError // Use RepositoryError
// ... other imports

interface ProviderRepository {
    val providers: StateFlow<DataState<RepositoryError, List<LLMProvider>>> // Public reactive data

    suspend fun loadProviders(): Either<RepositoryError, Unit> // Return RepositoryError
    suspend fun addProvider(request: AddProviderRequest): Either<RepositoryError, LLMProvider>
    // ... other CRUD methods
}

// New file: app/src/commonMain/kotlin/eu/torvian/chatbot/app/repository/DefaultProviderRepository.kt
package eu.torvian.chatbot.app.repository
// ... imports
class DefaultProviderRepository(
    private val providerApi: ProviderApi, // Inject ProviderApi (which now returns RepositoryError)
    private val applicationScope: CoroutineScope = ApplicationScope,
    private val ioDispatcher: CoroutineDispatcher = ioDispatcher
) : ProviderRepository {
    private val _providers = MutableStateFlow<DataState<RepositoryError, List<LLMProvider>>>(DataState.Idle)
    override val providers: StateFlow<DataState<RepositoryError, List<LLMProvider>>> = _providers.asStateFlow()

    override suspend fun loadProviders(): Either<RepositoryError, Unit> {
        if (_providers.value.isLoading) return Either.Right(Unit)
        _providers.update { DataState.Loading } // Set loading state immediately
        return providerApi.getAllProviders() // Call API
            .tap { newProviders ->
                _providers.value = DataState.Success(newProviders) // Update flow on success
            }
            .mapLeft { error -> // 'error' is RepositoryError
                _providers.value = DataState.Error(error) // Update flow on error
                error
            }
            .map { Unit }
    }
    // ... other CRUD methods will similarly update _providers flow, now handling RepositoryError
}
```

#### 6. ViewModel Layer Modifications for Migrated Features

*   **Purpose:** To adapt the `ModelConfigViewModel`, `ProviderConfigViewModel`, and `SettingsConfigViewModel` to use the new Repository layer and `DataState`.
*   **Changes:**
    *   **Constructor Parameters:** Update these ViewModels to inject `Repository` interfaces (e.g., `ModelRepository`) instead of `Api` interfaces (e.g., `ModelApi`).
    *   **StateFlow Types:** Change all relevant `MutableStateFlow`s (like `_modelConfigState`) to use `DataState<RepositoryError, T>`. Ensure you import `DataState` from `app.domain.contracts`.
    *   **`init` block `collect`:** In the `init` block, launch coroutines to `collect` from the repository's `StateFlow`s. For ViewModels needing multiple data streams, use `combine` to merge them. Update the ViewModel's public `MutableStateFlow`s based on these collected values.
    *   **Action Methods:** Change calls from `api.someCall()` to `repository.someCall()`. Adjust error handling in `.fold` blocks to use a `when (repositoryError)` statement to specifically handle `RepositoryError.NetworkError`, `RepositoryError.ServerError`, `RepositoryError.SerializationError`, and `RepositoryError.UnknownError`.
    *   **Remove Manual List Update Logic:** Functions like `updateModels` or direct list manipulations in `_modelConfigState.update` are no longer needed, as the repository's `StateFlow` handles automatic updates.
*   **Impact:** These ViewModels will now be reactive, decoupled from API specifics, and have granular error handling. Other ViewModels (not migrated) continue to use `UiState<ApiError, T>`.

**Code Example (Modified `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/ModelConfigViewModel.kt`)**

```kotlin
package eu.torvian.chatbot.app.viewmodel
// ... other imports
import eu.torvian.chatbot.app.domain.contracts.DataState // New import for DataState
import eu.torvian.chatbot.app.repository.ModelRepository // New import
import eu.torvian.chatbot.app.repository.ProviderRepository // New import
import eu.torvian.chatbot.common.api.RepositoryError // New import

class ModelConfigViewModel(
    private val modelRepository: ModelRepository, // Injected repository
    private val providerRepository: ProviderRepository, // Injected repository
    private val uiDispatcher: CoroutineDispatcher = Dispatchers.Main
) : ViewModel() {
    private val _modelConfigState = MutableStateFlow<DataState<RepositoryError, ModelConfigData>>(DataState.Idle) // DataState and RepositoryError
    // ... other state properties

    val modelConfigState: StateFlow<DataState<RepositoryError, ModelConfigData>> = _modelConfigState.asStateFlow()

    init {
        viewModelScope.launch(uiDispatcher) {
            combine(
                modelRepository.models, // Observe repository's models flow
                providerRepository.providers // Observe repository's providers flow
            ) { modelsDataState, providersDataState ->
                // Combine the DataStates into a single DataState<ModelConfigData>
                when {
                    modelsDataState is DataState.Loading || providersDataState is DataState.Loading -> DataState.Loading
                    modelsDataState is DataState.Error -> DataState.Error(modelsDataState.error)
                    providersDataState is DataState.Error -> DataState.Error(providersDataState.error)
                    modelsDataState is DataState.Success && providersDataState is DataState.Success -> {
                        DataState.Success(ModelConfigData(models = modelsDataState.data, providers = providersDataState.data))
                    }
                    else -> DataState.Idle
                }
            }.collect { combinedDataState ->
                _modelConfigState.value = combinedDataState // Update ViewModel's public state
                if (combinedDataState is DataState.Success) {
                    syncSelectedModelWithList(combinedDataState.data.models)
                }
            }
        }
    }

    fun loadModelsAndProviders() {
        if (_modelConfigState.value.isLoading) return
        viewModelScope.launch(uiDispatcher) {
            parZip(
                ioDispatcher,
                { modelRepository.loadModels() },
                { providerRepository.loadProviders() }
            ) { /* no direct handling here, combined flow will react */ _, _ -> }
        }
    }

    fun deleteModel(modelId: Long) {
        // ... safety checks
        viewModelScope.launch(uiDispatcher) {
            modelRepository.deleteModel(modelId) // Delegate to repository
                .fold(
                    ifLeft = { repositoryError -> // Handle RepositoryError
                        val errorMessage = when (repositoryError) {
                            is RepositoryError.ServerError -> {
                                when (repositoryError.apiError.code) { // Access original ApiError
                                    CommonApiErrorCodes.RESOURCE_IN_USE.code ->
                                        "Cannot delete model. It is currently linked to one or more chat sessions."
                                    else -> "Error deleting model: ${repositoryError.message}"
                                }
                            }
                            is RepositoryError.NetworkError -> "Network error: ${repositoryError.description}"
                            is RepositoryError.SerializationError -> "Data parsing error: ${repositoryError.description}"
                            is RepositoryError.UnknownError -> "Unexpected error: ${repositoryError.description}"
                        }
                        println(errorMessage) // Log or show UI error
                    },
                    ifRight = {
                        cancelDialog() // Repository handled list update
                    }
                )
        }
    }
    // ... other action methods updated similarly
}
```

#### 7. Utility Scope: `ApplicationScope`

*   **Purpose:** To provide a global `CoroutineScope` for long-lived coroutines within repositories that might need to persist beyond a ViewModel's lifecycle.
*   **Changes:**
    *   **Create New File:** `app/src/commonMain/kotlin/eu/torvian/chatbot/app/utils/misc/Coroutines.kt` if it doesn't exist, and define `ioDispatcher` and `ApplicationScope`.
*   **Impact:** Used by repositories for background tasks.

**Code Example (`app/src/commonMain/kotlin/eu/torvian/chatbot/app/utils/misc/Coroutines.kt`)**

```kotlin
// New file: app/src/commonMain/kotlin/eu/torvian/chatbot/app/utils/misc/Coroutines.kt
package eu.torvian.chatbot.app.utils.misc
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob

val ioDispatcher: CoroutineDispatcher = Dispatchers.IO
val ApplicationScope: CoroutineScope = CoroutineScope(SupervisorJob() + ioDispatcher)
```

#### 8. `SettingsFormState` Helper

*   **Purpose:** A utility function to easily set the `modelId` on a new `SettingsFormState`.
*   **Changes:**
    *   Add a `withModelId` extension function to `app/src/commonMain/kotlin/eu/torvian/chatbot/app/domain/contracts/SettingsFormState.kt`.
*   **Impact:** Simplifies the creation of new settings forms.

**Code Example (`app/src/commonMain/kotlin/eu/torvian/chatbot/app/domain/contracts/SettingsFormState.kt`)**

```kotlin
// app/src/commonMain/kotlin/eu/torvian/chatbot/app/domain/contracts/SettingsFormState.kt
// ... existing code
fun SettingsFormState.withModelId(modelId: Long): SettingsFormState {
    return when (this) {
        is SettingsFormState.Chat -> this.copy(modelId = modelId)
        is SettingsFormState.Embedding -> this.copy(modelId = modelId)
        // Add other SettingsFormState types here as they are added
    }
}
// ... rest of the file
```

---

### Implementation Steps Checklist for the Development Team

Please follow these steps in order to implement the changes for `Model`, `Provider`, and `Settings` features:

1.  **Create `RepositoryError.kt`:**
    *   Create the file `common/src/commonMain/kotlin/eu/torvian/chatbot/common/api/RepositoryError.kt` as described in Step 1.

2.  **Create `DataState.kt`:**
    *   Create the *new* file `app/src/commonMain/kotlin/eu/torvian/chatbot/app/domain/contracts/DataState.kt` as described in Step 2. **Do NOT modify `UiState.kt`.**

3.  **Create `RepositoryAwareBaseApiClient.kt`:**
    *   Create the file `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/RepositoryAwareBaseApiClient.kt` as described in Step 3. **Do NOT modify `BaseApiClient.kt`.**

4.  **Update `Api` Interfaces (`ModelApi`, `ProviderApi`, `SettingsApi`):**
    *   Modify the files `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ModelApi.kt`, `ProviderApi.kt`, and `SettingsApi.kt`.
    *   Change the `Either.Left` type in all suspend function signatures from `ApiError` to `RepositoryError`.

5.  **Modify Ktor API Client Implementations (`KtorModelApiClient`, `KtorProviderApiClient`, `KtorSettingsApiClient`):**
    *   Modify `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/KtorModelApiClient.kt`, `KtorProviderApiClient.kt`, and `KtorSettingsApiClient.kt`.
    *   **Change Base Class:** Update them to inherit from `RepositoryAwareBaseApiClient` instead of `BaseApiClient`.
    *   **Remove Direct `ApiError` Imports:** As `RepositoryError` now wraps `ApiError`, direct import of `ApiError` from `common.api` should be removed in these specific files (unless explicitly needed for other, non-API-related reasons, which should be rare).

6.  **Create Repository Interfaces:**
    *   Create the interface files `app/src/commonMain/kotlin/eu/torvian/chatbot/app/repository/ModelRepository.kt`, `ProviderRepository.kt`, and `SettingsRepository.kt`.
    *   Define the `StateFlow`s using `DataState<RepositoryError, List<T>>` and suspend functions returning `Either<RepositoryError, T>`.

7.  **Create Default Repository Implementations:**
    *   Create the implementation files `app/src/commonMain/kotlin/eu.torvian/chatbot/app/repository/DefaultModelRepository.kt`, `DefaultProviderRepository.kt`, and `DefaultSettingsRepository.kt`.
    *   Implement their respective interfaces, injecting the *newly modified* API client interfaces.
    *   Implement the CRUD methods, calling the API clients and updating the internal `MutableStateFlow` with `DataState<RepositoryError, T>`.

8.  **Update ViewModels (`ModelConfigViewModel`, `ProviderConfigViewModel`, `SettingsConfigViewModel`):**
    *   Modify `app/src/commonMain/kotlin/eu/torvian/chatbot/app/viewmodel/ModelConfigViewModel.kt`, `ProviderConfigViewModel.kt`, and `SettingsConfigViewModel.kt`.
    *   **Change Constructor Parameters:** Inject the new `Repository` interfaces.
    *   **Update `StateFlow` Types:** Change relevant `MutableStateFlow`s to use `DataState<RepositoryError, T>`.
    *   **Implement `init` block `collect`:** Add `collect` blocks to observe repository `StateFlow`s, using `combine` where necessary.
    *   **Update Action Methods:** Delegate calls to repositories and update error handling logic to `when (repositoryError)`.
    *   **Remove Manual List Update Logic:** Remove redundant code that manually updates data lists within the ViewModel, as the repository's `StateFlow` will now handle this reactively.

9.  **Create `ApplicationScope` Utility:**
    *   Ensure `app/src/commonMain/kotlin/eu/torvian/chatbot/app/utils/misc/Coroutines.kt` exists with `ioDispatcher` and `ApplicationScope` definitions.

10. **Add `SettingsFormState.withModelId()` Helper:**
    *   Add the `withModelId` extension function to `app/src/commonMain/kotlin/eu/torvian/chatbot/app/domain/contracts/SettingsFormState.kt`.

11. **Setup Dependency Injection (Crucial):**
    *   Ensure your DI framework (e.g., Koin, Hilt) is configured to provide instances of the *new* `KtorModelApiClient`, `KtorProviderApiClient`, `KtorSettingsApiClient` to the `DefaultModelRepository`, `DefaultProviderRepository`, `DefaultSettingsRepository` constructors.
    *   Then, configure the DI to provide these `Default...Repository` implementations to the respective `ModelConfigViewModel`, `ProviderConfigViewModel`, and `SettingsConfigViewModel`.

12. **Extend `SettingsFormState` for other `ModelSettings` types (Future Task):**
    *   This is a follow-up task to expand `SettingsFormState` and its related extension functions to cover all `LLMModelType`s.

This comprehensive plan provides all the necessary details for our team to implement these architectural improvements for the `Model`, `Provider`, and `Settings` features, ensuring a controlled and effective transition. Please don't hesitate to ask questions as you begin working through these steps.