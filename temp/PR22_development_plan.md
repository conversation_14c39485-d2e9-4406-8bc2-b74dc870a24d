# PR 22: Implement Message Actions UI (Edit, Delete, Reply, Copy) - Development Plan

## Overview

This PR implements the user interface components for message actions (Edit, Delete, Reply, Copy) as specified in Epic 3 (E3.S*). The goal is to provide users with intuitive controls to interact with individual messages in the chat interface.

## Current State Analysis

### Existing Infrastructure ✅
- **ViewModel Layer**: ChatViewModel with complete action methods already implemented
  - `startEditing()`, `saveEditing()`, `cancelEditing()` 
  - `deleteMessage()`
  - `startReplyTo()`, `cancelReply()`
  - Copy functionality missing from ViewModel
- **Use Cases**: Complete implementation exists
  - `EditMessageUseCase`, `DeleteMessageUseCase`, `ReplyUseCase`
- **State Management**: Full editing/reply state tracking in ChatAreaState
  - `editingMessage`, `editingContent`, `replyTargetMessage`
- **Action Contracts**: Complete ChatAreaActions interface with all required methods
- **UI Buttons**: Basic action buttons already exist but are commented out
  - <PERSON><PERSON><PERSON>on, CopyButton, RegenerateButton components exist
- **Backend API**: Full message CRUD operations available

### Missing Components ❌
- **Copy Functionality**: No clipboard integration (planned for PR 25)
- **Delete Confirmation Dialog**: No confirmation UI for message deletion
- **Inline Editing UI**: No UI for editing messages in place
- **Reply Button**: Missing reply action button
- **Action Integration**: MessageActions callbacks not wired to ChatAreaActions
- **Visual Feedback**: No success/error feedback for actions

## Technical Implementation Approach

### Architecture Patterns
- **MVVM Pattern**: Continue using existing ViewModel → State → UI flow
- **Compose Material 3**: Use Material Design 3 components for consistency
- **State-driven UI**: All UI changes driven by state updates from ViewModel
- **Action Delegation**: UI actions delegate to ViewModel use cases

### Integration Points
- **ChatArea.kt**: Main integration point for message action UI
- **MessageItem**: Individual message component where actions are displayed
- **ChatAreaActions**: Contract interface for action callbacks
- **ChatViewModel**: Existing action methods will be wired to UI

## User Experience Flow

### Edit Message Flow
1. User hovers over user message → Edit button appears
2. User clicks Edit → Message content replaced with TextField
3. User modifies content → Real-time state updates
4. User clicks Save → API call → Success feedback → Return to display mode
5. User clicks Cancel → Discard changes → Return to display mode

### Delete Message Flow
1. User hovers over message → Delete button appears
2. User clicks Delete → Confirmation dialog appears
3. User confirms → API call → Success feedback → Message removed
4. User cancels → Dialog dismissed, no changes

### Reply Message Flow
1. User hovers over message → Reply button appears
2. User clicks Reply → Input area shows reply target banner
3. User types message → Send as reply to selected message
4. User cancels reply → Clear reply target, return to normal input

### Copy Message Flow
1. User hovers over message → Copy button appears
2. User clicks Copy → Content copied to clipboard
3. Brief success feedback shown (tooltip/snackbar)

## Step-by-Step Implementation Tasks

### Phase 1: Core Action Integration (High Priority)
1. **Wire MessageActions to ChatAreaActions**
   - Uncomment and connect action callbacks in ChatArea.kt
   - Update MessageActions construction to use actions contract
   - Test basic action triggering

2. **Implement Reply Button UI**
   - Add ReplyButton component following existing button patterns
   - Add to GeneralMessageControls for all message types
   - Wire to onStartReplyTo action

3. **Implement Delete Confirmation Dialog**
   - Create DeleteMessageDialog component following SessionListPanel patterns
   - Add dialog state management to ChatArea or parent component
   - Wire to onDeleteMessage action with confirmation flow

### Phase 2: Inline Editing UI (High Priority)
4. **Create Inline Editing Components**
   - MessageEditingField component with TextField, Save/Cancel buttons
   - Replace message content display when editingMessage matches current message
   - Handle keyboard shortcuts (Enter to save, Escape to cancel)

5. **Integrate Editing State**
   - Update MessageItem to check editingMessage state
   - Conditionally render editing UI vs display UI
   - Wire editing actions to ChatAreaActions

### Phase 3: Visual Polish & Feedback (Medium Priority)
6. **Enhance Action Button Visibility**
   - Improve hover states and transitions
   - Ensure proper spacing and alignment
   - Add loading states for async actions

7. **Add Success/Error Feedback**
   - Integrate with existing EventBus for snackbar notifications
   - Add specific messages for each action type
   - Handle error states gracefully

### Phase 4: Copy Functionality Preparation (Low Priority)
8. **Prepare Copy Action Structure**
   - Add onCopyMessage to ChatAreaActions (stub implementation)
   - Wire CopyButton to action (will be no-op until PR 25)
   - Document clipboard integration requirements

## State Management Considerations

### Editing State Flow
```
Normal Display → Edit Clicked → Editing Mode → Save/Cancel → Normal Display
                                     ↓
                              Real-time content updates
```

### Delete State Flow
```
Normal Display → Delete Clicked → Confirmation Dialog → Confirm → API Call → Success → Remove Message
                                        ↓
                                    Cancel → Normal Display
```

### Reply State Flow
```
Normal Display → Reply Clicked → Reply Target Set → Input Banner Shown → Send/Cancel → Clear Target
```

## Testing Strategy

### Unit Tests
- **ChatViewModel Actions**: Test action method calls and state updates
- **Use Case Integration**: Verify proper use case execution
- **State Transitions**: Test editing/reply state management

### UI Component Tests
- **MessageItem Rendering**: Test conditional rendering based on state
- **Action Button Visibility**: Test hover states and button appearance
- **Dialog Interactions**: Test confirmation dialog behavior
- **Inline Editing**: Test editing field behavior and validation

### Integration Tests
- **End-to-End Flows**: Test complete user workflows
- **Error Handling**: Test API error scenarios
- **State Consistency**: Verify UI state matches ViewModel state

### Test Utilities to Use
- **MockK**: For mocking dependencies in ViewModel tests
- **Compose Test**: For UI component testing
- **TestCoroutineScheduler**: For testing async operations
- **FlowTestUtils**: For testing StateFlow emissions

## Potential Challenges & Mitigation Strategies

### Challenge 1: Complex State Management
**Risk**: Managing multiple concurrent states (editing, reply, delete confirmation)
**Mitigation**: 
- Use sealed classes for dialog states (following SessionListPanel pattern)
- Ensure only one action state active at a time
- Clear state on navigation/session changes

### Challenge 2: UI Performance
**Risk**: Frequent recomposition during editing
**Mitigation**:
- Use `remember` for stable action callbacks
- Optimize key usage in LazyColumn
- Debounce editing content updates if needed

### Challenge 3: Keyboard Handling
**Risk**: Inconsistent keyboard shortcuts in editing mode
**Mitigation**:
- Implement consistent KeyboardActions
- Handle focus management properly
- Test across different input scenarios

### Challenge 4: Error Recovery
**Risk**: Failed actions leaving UI in inconsistent state
**Mitigation**:
- Implement proper error boundaries
- Reset state on errors
- Provide clear error feedback to users

## Dependencies & Prerequisites

### Internal Dependencies
- **PR 20**: Chat Area UI (Message Display) - ✅ Complete
- **PR 15**: Chat Actions ViewModel Logic - ✅ Complete
- **Existing Backend APIs**: Message CRUD operations - ✅ Available

### External Dependencies
- **Compose Material 3**: AlertDialog, TextField, IconButton
- **Kotlin Coroutines**: For async action handling
- **Arrow Core**: For Either-based error handling

### Future Dependencies
- **PR 25**: Copy to Clipboard implementation
- **Clipboard API**: Platform-specific clipboard access

## Success Criteria

### Functional Requirements ✅
- [ ] Users can edit their own messages with inline editing UI
- [ ] Users can delete any message with confirmation dialog
- [ ] Users can reply to any message with visual reply target
- [ ] Users can copy message content (UI ready, functionality in PR 25)
- [ ] All actions provide appropriate feedback

### Technical Requirements ✅
- [ ] Actions integrate with existing ViewModel architecture
- [ ] UI follows Material 3 design patterns
- [ ] State management is consistent and predictable
- [ ] Error handling is robust and user-friendly
- [ ] Performance is maintained during interactions

### Quality Requirements ✅
- [ ] Comprehensive test coverage for new components
- [ ] Accessibility support for all interactive elements
- [ ] Consistent visual design with existing UI
- [ ] Proper keyboard navigation support

## Implementation Timeline

**Estimated Effort**: 3-4 days for experienced developer

**Phase 1** (Day 1): Core action integration and reply button
**Phase 2** (Day 2): Delete confirmation and inline editing UI  
**Phase 3** (Day 3): Visual polish and feedback integration
**Phase 4** (Day 4): Testing, documentation, and copy preparation

This plan provides a structured approach to implementing message actions UI while leveraging the existing robust backend and ViewModel infrastructure.

## Detailed Component Specifications

### 1. MessageActions Integration
**File**: `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/ChatArea.kt`

**Current State**: MessageActions callbacks are commented out (lines 178-180)
```kotlin
// onEditMessage = actions::onStartEditing,
// onCopyMessage = actions::onCopyMessageContent,
// onRegenerateMessage = actions::onRegenerateAssistantMessage
```

**Required Changes**:
- Uncomment and wire `onEditMessage = actions::onStartEditing`
- Add `onReplyMessage = actions::onStartReplyTo`
- Add `onDeleteMessage = { message -> actions::onDeleteMessage(message.id) }`
- Keep `onCopyMessage` as null until PR 25

### 2. Reply Button Component
**New Component**: Add to GeneralMessageControls in ChatArea.kt

```kotlin
@Composable
private fun ReplyButton(message: ChatMessage, onReplyMessage: ((ChatMessage) -> Unit)?) {
    if (onReplyMessage != null) {
        PlainTooltipBox(text = "Reply to message") {
            IconButton(
                onClick = { onReplyMessage(message) },
                modifier = Modifier.size(24.dp)
            ) {
                Icon(
                    Icons.Default.Reply,
                    contentDescription = "Reply to message",
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}
```

### 3. Delete Confirmation Dialog
**Implementation Pattern**: Follow SessionListPanel's DeleteSessionDialog pattern

```kotlin
@Composable
private fun DeleteMessageDialog(
    message: ChatMessage,
    onDeleteConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Delete Message?") },
        text = {
            Text("Are you sure you want to delete this message? This action cannot be undone.")
        },
        confirmButton = {
            Button(
                onClick = onDeleteConfirm,
                colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.error)
            ) { Text("Delete") }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) { Text("Cancel") }
        }
    )
}
```

### 4. Inline Editing UI
**Component**: Replace message content display conditionally

```kotlin
@Composable
private fun MessageContent(
    message: ChatMessage,
    isEditing: Boolean,
    editingContent: String,
    onUpdateEditingContent: (String) -> Unit,
    onSaveEditing: () -> Unit,
    onCancelEditing: () -> Unit,
    contentColor: Color
) {
    if (isEditing) {
        MessageEditingField(
            content = editingContent,
            onContentChange = onUpdateEditingContent,
            onSave = onSaveEditing,
            onCancel = onCancelEditing
        )
    } else {
        Text(
            text = message.content,
            style = MaterialTheme.typography.bodyLarge,
            color = contentColor
        )
    }
}
```

## Code Integration Points

### ChatAreaActions Extension
**File**: `app/src/commonMain/kotlin/eu/torvian/chatbot/app/domain/contracts/ChatAreaActions.kt`

**Missing Method**: Add copy message action (stub for PR 25)
```kotlin
/**
 * Callback for when the user wants to copy message content to clipboard.
 * @param message The message whose content should be copied.
 */
fun onCopyMessage(message: ChatMessage)
```

### ChatScreen Integration
**File**: `app/src/commonMain/kotlin/eu/torvian/chatbot/app/compose/ChatScreen.kt`

**Required Addition**: Wire copy action in chatAreaActions (line 131)
```kotlin
override fun onCopyMessage(message: ChatMessage) {
    // TODO: Implement in PR 25 - Copy to Clipboard
}
```

## Error Handling Strategy

### API Error Scenarios
1. **Edit Message Failure**: Show inline error, keep editing mode active
2. **Delete Message Failure**: Show snackbar error, keep message visible
3. **Network Errors**: Use existing EventBus error notification system

### State Recovery
- **Edit Cancellation**: Reset to original message content
- **Dialog Dismissal**: Clear dialog state, no side effects
- **Session Navigation**: Clear all editing/reply states

## Accessibility Considerations

### Keyboard Navigation
- **Tab Order**: Edit → Reply → Copy → Delete → Branch Navigation
- **Keyboard Shortcuts**:
  - Enter: Save editing
  - Escape: Cancel editing/reply
  - Space/Enter: Activate buttons

### Screen Reader Support
- **Content Descriptions**: All action buttons have descriptive labels
- **State Announcements**: Editing mode changes announced
- **Error Messages**: Accessible error feedback

### Focus Management
- **Edit Mode**: Focus moves to editing TextField
- **Dialog Open**: Focus moves to dialog
- **Action Complete**: Focus returns to appropriate element

## Performance Optimization

### Recomposition Minimization
- **Stable Callbacks**: Use `remember` for action callbacks
- **Key Optimization**: Proper key usage in LazyColumn items
- **State Scoping**: Minimize state dependencies in composables

### Memory Management
- **Dialog State**: Use sealed classes to prevent memory leaks
- **Callback References**: Avoid capturing unnecessary variables
- **Resource Cleanup**: Proper disposal of temporary states

## Testing Implementation Details

### Unit Test Files to Create/Update
1. **ChatAreaTest.kt**: Add message action tests
2. **MessageItemTest.kt**: New file for individual message component tests
3. **ChatViewModelTest.kt**: Update with action integration tests

### Test Scenarios
```kotlin
@Test
fun messageActions_editButton_triggersEditingState() {
    // Test edit button click triggers editing mode
}

@Test
fun messageActions_deleteButton_showsConfirmationDialog() {
    // Test delete button shows confirmation
}

@Test
fun messageActions_replyButton_setsReplyTarget() {
    // Test reply button sets reply target
}

@Test
fun inlineEditing_saveButton_callsViewModel() {
    // Test save button calls ViewModel method
}
```

This comprehensive plan ensures all aspects of message actions UI are properly implemented while maintaining code quality and user experience standards.
