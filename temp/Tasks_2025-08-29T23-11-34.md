[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[/] NAME:PR 24: Implement Settings Screen UI (E4.S*, E5.S*) DESCRIPTION:Complete implementation of the Settings Screen UI for managing LLM providers, models, and settings profiles. This is the main epic task that encompasses all Epic 4 and Epic 5 user stories related to UI implementation.
--[ ] NAME:Phase 1: Core UI Infrastructure DESCRIPTION:Implement the foundational UI components and layout structure for the Settings Screen
---[ ] NAME:Implement main SettingsScreen layout with tab navigation DESCRIPTION:Create the main container composable with Material 3 TabRow for navigating between Providers, Models, and Settings sections
---[ ] NAME:Create reusable form components DESCRIPTION:Implement ConfigTextField, ConfigDropdown, ConfigCheckbox, ConfigNumberField, and CredentialField components with validation support
---[ ] NAME:Set up error display and loading state components DESCRIPTION:Create reusable components for displaying UiState.Error and UiState.Loading states with proper Material 3 styling
---[ ] NAME:Integrate ViewModels with UI components DESCRIPTION:Connect the existing ProviderConfigViewModel, ModelConfigViewModel, and SettingsConfigViewModel to the UI using collectAsState
--[ ] NAME:Phase 2: Providers Management UI DESCRIPTION:Implement the complete UI for managing LLM providers including CRUD operations and credential management
---[ ] NAME:Implement ProvidersTab with list and detail views DESCRIPTION:Create the providers management interface with master-detail layout showing provider list and selected provider details
---[ ] NAME:Create AddProviderDialog with form validation DESCRIPTION:Implement modal dialog for adding new LLM providers with all required fields and client-side validation
---[ ] NAME:Implement credential management UI DESCRIPTION:Create masked input fields for API key management and status indicators without exposing sensitive data
---[ ] NAME:Add provider deletion with confirmation dialogs DESCRIPTION:Implement delete functionality with proper confirmation dialogs and error handling for providers in use
--[ ] NAME:Phase 3: Models Management UI DESCRIPTION:Implement the complete UI for managing LLM models including provider associations and model configuration
---[ ] NAME:Implement ModelsTab with provider-filtered views DESCRIPTION:Create models management interface showing models grouped by provider with filtering capabilities
---[ ] NAME:Create AddModelDialog with provider selection DESCRIPTION:Implement modal dialog for adding new models with provider dropdown and all required model configuration fields
---[ ] NAME:Implement model editing with validation DESCRIPTION:Create model detail/edit interface with proper validation for all model properties and provider associations
---[ ] NAME:Add model deletion with dependency checking DESCRIPTION:Implement model deletion with confirmation dialogs and proper error handling for models in use by sessions
--[ ] NAME:Phase 4: Settings Management UI DESCRIPTION:Implement the UI for managing model settings profiles including ChatModelSettings configuration
---[ ] NAME:Implement SettingsTab with model selection DESCRIPTION:Create settings management interface with model dropdown selector and settings profiles list for the selected model
---[ ] NAME:Create settings profile management interface DESCRIPTION:Implement list view for ChatModelSettings profiles with add/edit/delete capabilities for the selected model
---[ ] NAME:Implement ChatModelSettings form with all parameters DESCRIPTION:Create comprehensive form for editing ChatModelSettings including system message, temperature, tokens, and other parameters
---[ ] NAME:Add JSON editor for custom parameters DESCRIPTION:Implement JSON input field with validation for custom parameters in ChatModelSettings
--[ ] NAME:Phase 5: Polish and Integration DESCRIPTION:Final integration, testing, and polish of the complete Settings Screen implementation
---[ ] NAME:Implement comprehensive error handling and user feedback DESCRIPTION:Add proper error display, toast notifications, and user feedback for all operations across the Settings Screen
---[ ] NAME:Add confirmation dialogs for destructive operations DESCRIPTION:Implement confirmation dialogs for all delete operations with clear warnings about consequences
---[ ] NAME:Optimize UI responsiveness and loading states DESCRIPTION:Ensure all operations show appropriate loading indicators and the UI remains responsive during API calls
---[ ] NAME:Integration testing with main application DESCRIPTION:Test the complete Settings Screen integration with the main app navigation and ensure proper state management
---[ ] NAME:Documentation and code cleanup DESCRIPTION:Add comprehensive KDoc documentation, clean up code, and ensure consistency with project standards