## Architectural Refactoring: A Refined Error Handling Strategy

Hello Team,

This document outlines an important architectural upgrade for our application. The goal is to refactor our data handling logic to be more robust, reliable, and easier to maintain. We will begin by applying this new architecture to the **Model, Provider, and Settings** configuration features.

### Why We're Doing This: Addressing Current Challenges

As our application has grown, our current architecture—where ViewModels directly call API services—has presented some common challenges that this refactoring will solve:

1.  **Stale Data and Lack of Synchronization:** When one part of the app updates data (e.g., adds a new provider), other parts are not automatically notified and can display outdated information. This requires manual data management, which is complex and error-prone.
2.  **Inconsistent and Limited Error Handling:** Our current `ApiError` is great for structured server responses but struggles to represent client-side issues like network timeouts, no internet connection, or data parsing failures. This leads to generic, less-helpful error messages.
3.  **Tight Coupling:** Our ViewModels are too closely tied to the API clients, making it difficult to introduce new features like caching or offline support without significant changes to the UI layer.

### The Solution: The Repository Pattern with a Two-Tier Error System

We are adopting the **Repository Pattern with Reactive Data Streams**. This involves:

*   **A New Repository Layer:** ViewModels will no longer talk directly to API clients. They will communicate with new **Repository** classes, which will act as the single, authoritative source for our data.
*   **Reactive Data with `StateFlow`:** Repositories will expose data using Kotlin `StateFlow`s. When data changes in the repository, all subscribing ViewModels will automatically receive the update.
*   **A Robust Two-Tier Error System:** We are introducing a clearer, more powerful error hierarchy to handle all possible failures gracefully.

### Phased Migration Strategy

To manage risk, this will be a **phased migration**. We will only refactor the `Model`, `Provider`, and `Settings` features for now.

*   The existing `BaseApiClient` and `UiState<ApiError, T>` class **will remain untouched** and continue to be used by other, non-migrated parts of the application.
*   We will introduce new, parallel components (`BaseApiResourceClient`, `RepositoryError`, etc.) and use the existing `DataState` class for the migrated features.

---

### Final Code Implementation

Here is the final design for our new, refined error classes and how they fit into the architecture.

#### 1. Low-Level: `ApiResourceError.kt`

This class represents errors that happen *directly during an API interaction*. It is specific to the API Service layer.

**File: `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ApiResourceError.kt`**

```kotlin
package eu.torvian.chatbot.app.service.api

import eu.torvian.chatbot.common.api.ApiError

/**
 * A sealed class representing errors that can occur when interacting with an API resource.
 * Each subclass provides a detailed 'message' for logging and debugging.
 */
sealed class ApiResourceError {
    abstract val message: String
    abstract val cause: Throwable?

    data class NetworkError(
        val description: String,
        override val cause: Throwable?
    ) : ApiResourceError() {
        override val message: String get() = "Network Error: $description" + (cause?.message?.let { " - Caused by: $it" } ?: "")

        companion object {
            fun from(throwable: Throwable): NetworkError {
                return NetworkError(throwable.message ?: "An unknown network error occurred.", throwable)
            }
        }
    }

    data class ServerError(
        val apiError: ApiError,
        override val cause: Throwable? = null
    ) : ApiResourceError() {
        override val message: String get() = "Server Error (Code: ${apiError.code}): ${apiError.message}"
    }

    data class SerializationError(
        val description: String,
        override val cause: Throwable?
    ) : ApiResourceError() {
        override val message: String get() = "Serialization Error: $description" + (cause?.message?.let { " - Caused by: $it" } ?: "")

        companion object {
            fun from(throwable: Throwable): SerializationError {
                return SerializationError("Failed to parse API response", throwable)
            }
        }
    }

    data class UnknownError(
        val description: String,
        override val cause: Throwable?
    ) : ApiResourceError() {
        override val message: String get() = "Unknown API Error: $description" + (cause?.message?.let { " - Caused by: $it" } ?: "")

        companion object {
            fun from(throwable: Throwable): UnknownError {
                return UnknownError(throwable.message ?: "An unexpected error occurred.", throwable)
            }
        }
    }
}

fun ApiError.toApiResourceError(): ApiResourceError.ServerError = ApiResourceError.ServerError(this)
```

#### 2. High-Level: `RepositoryError.kt`

This class represents errors at the repository level. It is specific to the Repository layer and wraps the low-level `ApiResourceError` to add operational context.

**File: `app/src/commonMain/kotlin/eu/torvian/chatbot/app/repository/RepositoryError.kt`**

```kotlin
package eu.torvian.chatbot.app.repository

import eu.torvian.chatbot.app.service.api.ApiResourceError

/**
 * A sealed class representing errors that can originate from the Repository layer.
 * Its 'message' property provides a comprehensive, technical error string suitable for the UI.
 */
sealed class RepositoryError {
    abstract val message: String
    abstract val cause: Throwable?

    data class DataFetchError(
        val apiResourceError: ApiResourceError,
        val contextMessage: String? = null
    ) : RepositoryError() {
        override val message: String
            get() = if (contextMessage != null) {
                "$contextMessage: ${apiResourceError.message}"
            } else {
                apiResourceError.message
            }

        override val cause: Throwable?
            get() = apiResourceError.cause
    }

    data class UnknownRepositoryError(
        val description: String,
        override val cause: Throwable?
    ) : RepositoryError() {
        override val message: String get() = "Unknown Repository Error: $description" + (cause?.message?.let { " - Caused by: $it" } ?: "")

        companion object {
            fun from(throwable: Throwable): UnknownRepositoryError {
                return UnknownRepositoryError("An unknown repository error occurred.", throwable)
            }
        }
    }
}

fun ApiResourceError.toRepositoryError(
    contextMessage: String? = null
): RepositoryError.DataFetchError {
    return RepositoryError.DataFetchError(this, contextMessage)
}
```

#### 3. New Base API Client: `BaseApiResourceClient`

This new base client will produce `ApiResourceError` and will be used by our migrated API client implementations.

**File: `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/BaseApiResourceClient.kt`**

```kotlin
package eu.torvian.chatbot.app.service.api.ktor

import arrow.core.Either
import arrow.core.left
import eu.torvian.chatbot.app.service.api.ApiResourceError
import eu.torvian.chatbot.app.service.api.toApiResourceError
import eu.torvian.chatbot.app.utils.misc.KmpLogger
import eu.torvian.chatbot.app.utils.misc.ioDispatcher
import eu.torvian.chatbot.app.utils.misc.kmpLogger
import eu.torvian.chatbot.common.api.ApiError
import io.ktor.client.*
// ... other imports

abstract class BaseApiResourceClient(protected val client: HttpClient) {
    // ... (Implementation remains the same as the previous step, but with updated imports)
}
```

---

### How to Use the New Classes (Impact on Other Layers)

#### 1. API Client Layer

The Ktor implementations for `Model`, `Provider`, and `Settings` will now inherit from `BaseApiResourceClient` and import `ApiResourceError` from its new package.

#### 2. Repository Layer

The repository receives an `ApiResourceError` and adds its own operational context to create the final `RepositoryError`.

**Example: `deleteModel` method in `DefaultModelRepository`**

```kotlin
import eu.torvian.chatbot.app.repository.RepositoryError
import eu.torvian.chatbot.app.repository.toRepositoryError
import eu.torvian.chatbot.app.service.api.ApiResourceError

// ... inside DefaultModelRepository
override suspend fun deleteModel(modelId: Long): Either<RepositoryError, Unit> {
    return modelApi.deleteModel(modelId)
        .fold(
            ifLeft = { apiResourceError ->
                // The repository adds high-level context here.
                val context = if (
                    apiResourceError is ApiResourceError.ServerError &&
                    apiResourceError.apiError.code == CommonApiErrorCodes.RESOURCE_IN_USE.code
                ) { "..." } else { "Failed to delete model" }
                
                // Convert to RepositoryError, preserving all information.
                apiResourceError.toRepositoryError(contextMessage = context).left()
            },
            ifRight = { /* ... handle success */ }
        )
}
```

#### 3. ViewModel Layer

The ViewModel is completely shielded from the complexity of the underlying errors and **simply uses the `.message` property** to get a comprehensive, ready-to-display error string.

**Example: `deleteModel` method in `ModelConfigViewModel`**

```kotlin
import eu.torvian.chatbot.app.repository.RepositoryError // Import from new location

// ... inside ModelConfigViewModel
fun deleteModel(modelId: Long) {
    viewModelScope.launch(uiDispatcher) {
        modelRepository.deleteModel(modelId)
            .fold(
                ifLeft = { repositoryError ->
                    // The ViewModel simply uses the .message property. No helpers needed.
                    val errorMessage = repositoryError.message
                    println(errorMessage) 
                },
                ifRight = { /* ... handle success */ }
            )
    }
}
```

---

### Implementation Checklist

1.  **Create `ApiResourceError.kt`**
    *   Create the file `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ApiResourceError.kt`.
    *   Implement the `ApiResourceError` sealed class and its helpers as detailed above.

2b.  **Update `RepositoryError.kt`**
    *   Create the file `app/src/commonMain/kotlin/eu/torvian/chatbot/app/repository/RepositoryError.kt`.
    *   Implement the `RepositoryError` sealed class and its `toRepositoryError` helper as detailed above.

3.  **Create `BaseApiResourceClient.kt`:**
    *   Create the file `app/src/commonMain/kotlin/eu/torvian/chatbot/app/service/api/ktor/BaseApiResourceClient.kt`.
    *   Ensure it imports `ApiResourceError` from its new package.

4.  **Update Migrated API Interfaces and Implementations:**
    *   Update `ModelApi`, `ProviderApi`, `SettingsApi` to return `Either<ApiResourceError, T>`.
    *   Update their Ktor implementations (`KtorModelApiClient`, etc.) to inherit from `BaseApiResourceClient` and update their `import` statements.

5.  **Update Repository Interfaces and Implementations:**
    *   Update `ModelRepository`, `ProviderRepository`, `SettingsRepository` and their default implementations.
    *   Update all `import` statements to point to the new locations for `RepositoryError` and `ApiResourceError`.
    *   Ensure methods correctly call `apiResourceError.toRepositoryError(contextMessage = ...)` to handle failures.

6.  **Update Migrated ViewModels:**
    *   Update `ModelConfigViewModel`, `ProviderConfigViewModel`, and `SettingsConfigViewModel`.
    *   Change constructor parameters to inject repositories.
    *   Update them to use `DataState<RepositoryError, T>`, ensuring `RepositoryError` is imported from its new package.
    *   In error handling blocks, directly use the `repositoryError.message` property to get the final error string.

7.  **Update Dependency Injection and Imports:**
    *   This is a crucial step. Go through the DI setup and all modified files (`DataState`, Repositories, ViewModels, etc.) and update all `import` statements to reflect the new package locations for `ApiResourceError` and `RepositoryError`.

This final design provides a powerful, flexible, and clean error handling system. It clearly separates concerns, preserves vital debugging information, and drastically simplifies the code in our higher-level components.