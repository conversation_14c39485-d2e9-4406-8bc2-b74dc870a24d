# Known bugs

## Scrollbar acting weird on Desktop target
Observed behavior:
  - Chat area: Sudden changes in (vertical) scrollbar size while scrolling.
Could this be related to the use of LazyColumn? This needs to be investigated further.
  - Settings screen: When updating/adding providers on the Providers tab, the changes are not reflected on the Models tab.
  - Settings screen: Details panels are not scrollable.