# TODO list

- move ChatAreaActions, ChatAreaState to compose.chatarea
- move SessionListActions, SessionListState to compose.sessionlist
- In ProvidersTab.kt, make the when block operate on UiState subtype directly.
- On the Models tab, move the plus button to the master panel.. similar to how its done in the Providers tab.
- Rename RepositoryError in Api classes to ApiResourceError. RepositoryError class needs redefinition and will wrap ApiResourceError in sealed class. Add helper function to convert ApiResourceError to RepositoryError.