package eu.torvian.chatbot.common.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient
import kotlinx.serialization.json.JsonObject

/**
 * Represents a specific settings profile for an LLM model, categorized by its [LLMModelType].
 * This sealed interface ensures type safety by providing specific parameters relevant to
 * each model type, while keeping common settings in the base interface.
 *
 * Used as a shared data model between frontend and backend API communication.
 *
 * @property id Unique identifier for the settings profile (Database PK).
 * @property modelId Foreign key to the associated [LLMModel].
 * @property name The display name of the settings profile (e.g., "Default", "Creative").
 * @property modelType The type of the LLM model these settings apply to.
 * @property customParams Arbitrary model-specific or provider-specific parameters not covered
 *                        by strongly-typed fields, stored as a [JsonObject]. This acts as a
 *                        flexible extension point for parameters specific to a provider or
 *                        a particular model variant not broadly categorized here.
 */
@Serializable
sealed class ModelSettings {
    abstract val id: Long
    abstract val modelId: Long
    abstract val name: String
    abstract val modelType: LLMModelType
    abstract val customParams: JsonObject?
}

/**
 * Settings specifically for [LLMModelType.CHAT] models. These models are designed for
 * multi-turn conversational interactions and text generation.
 *
 * @property id Unique identifier for the settings profile (Database PK).
 * @property modelId Foreign key to the associated [LLMModel].
 * @property name The display name of the settings profile (e.g., "Default", "Creative").
 * @property systemMessage The system message/prompt to include in the conversation context,
 *                         guiding the model's behavior and persona.
 * @property temperature Sampling temperature for text generation. Controls randomness: higher values
 *                          mean more random outputs, lower values mean more deterministic outputs.
 *                          Typically between 0.0 and 2.0.
 * @property maxTokens Maximum number of tokens (words or word parts) to generate in the response.
 * @property topP Top P sampling (nucleus sampling) for text generation. Controls diversity by
 *                   considering only the most probable tokens whose cumulative probability exceeds `topP`.
 *                   Typically between 0.0 and 1.0.
 * @property topK Top K sampling for text generation. Limits the model's token selection to the `topK`
 *                   most probable next tokens.
 * @property stopSequences A list of strings that, if generated by the model, will cause it to stop
 *                             generating further tokens immediately.
 * @property stream Whether to stream the response back as it's generated.
 * @property customParams Arbitrary model-specific or provider-specific parameters not covered
 *                        by strongly-typed fields, stored as a [JsonObject].
 */
@Serializable
@SerialName("chat")
data class ChatModelSettings(
    override val id: Long,
    override val modelId: Long,
    override val name: String,
    val systemMessage: String? = null,
    val temperature: Float? = null,
    val maxTokens: Int? = null,
    val topP: Float? = null,
    val topK: Int? = null,
    val stopSequences: List<String>? = null,
    val stream: Boolean = true,
    override val customParams: JsonObject? = null
) : ModelSettings() {
    @Transient
    override val modelType: LLMModelType = LLMModelType.CHAT

    init {
        require(!name.isBlank()) { "Settings name cannot be blank." }
        require(temperature == null || (temperature >= 0f && temperature <= 2f)) {
            "Temperature must be between 0.0 and 2.0 (inclusive)."
        }
        require(maxTokens == null || maxTokens > 0) {
            "Max tokens must be positive."
        }
        require(topP == null || (topP >= 0f && topP <= 1f)) {
            "Top P must be between 0.0 and 1.0 (inclusive)."
        }
        require(topK == null || topK > 0) {
            "Top K must be positive."
        }
    }
}

/**
 * Settings specifically for [LLMModelType.COMPLETION] models. These are typically older
 * models designed for single-turn text completion, where the model continues a given prompt.
 *
 * @property id Unique identifier for the settings profile (Database PK).
 * @property modelId Foreign key to the associated [LLMModel].
 * @property name The display name of the settings profile (e.g., "Default", "Creative").
 * @property suffix An optional suffix that gets appended to the prompt after the user's input,
 *                      guiding the completion towards a desired format or continuation.
 * @property temperature Sampling temperature for text generation. Controls randomness: higher values
 *                          mean more random outputs, lower values mean more deterministic outputs.
 *                          Typically between 0.0 and 2.0.
 * @property maxTokens Maximum number of tokens (words or word parts) to generate in the response.
 * @property topP Top P sampling (nucleus sampling) for text generation. Controls diversity by
 *                   considering only the most probable tokens whose cumulative probability exceeds `topP`.
 *                   Typically between 0.0 and 1.0.
 * @property topK Top K sampling for text generation. Limits the model's token selection to the `topK`
 *                   most probable next tokens.
 * @property stopSequences A list of strings that, if generated by the model, will cause it to stop
 *                             generating further tokens immediately.
 * @property customParams Arbitrary model-specific or provider-specific parameters not covered
 *                        by strongly-typed fields, stored as a [JsonObject].
 */
@Serializable
@SerialName("completion")
data class CompletionModelSettings(
    override val id: Long,
    override val modelId: Long,
    override val name: String,
    val suffix: String? = null,
    val temperature: Float? = null,
    val maxTokens: Int? = null,
    val topP: Float? = null,
    val topK: Int? = null,
    val stopSequences: List<String>? = null,
    override val customParams: JsonObject? = null
) : ModelSettings() {
    @Transient
    override val modelType: LLMModelType = LLMModelType.COMPLETION

    init {
        require(!name.isBlank()) { "Settings name cannot be blank." }
        require(temperature == null || (temperature >= 0f && temperature <= 2f)) {
            "Temperature must be between 0.0 and 2.0 (inclusive)."
        }
        require(maxTokens == null || maxTokens > 0) {
            "Max tokens must be positive."
        }
        require(topP == null || (topP >= 0f && topP <= 1f)) {
            "Top P must be between 0.0 and 1.0 (inclusive)."
        }
        require(topK == null || topK > 0) {
            "Top K must be positive."
        }
    }
}

/**
 * Settings specifically for [LLMModelType.EMBEDDING] models. These models convert text
 * into numerical vector representations.
 *
 * @property id Unique identifier for the settings profile (Database PK).
 * @property modelId Foreign key to the associated [LLMModel].
 * @property name The display name of the settings profile (e.g., "Default", "Creative").
 * @property dimensions The desired output dimension (length) for the embedding vector,
 *                          if the model supports variable dimensions.
 * @property encodingFormat The encoding format for the embedding output (e.g., "float" for
 *                            floating-point numbers, "base64" for a base64 encoded string).
 * @property customParams Arbitrary model-specific or provider-specific parameters not covered
 *                        by strongly-typed fields, stored as a [JsonObject].
 */
@Serializable
@SerialName("embedding")
data class EmbeddingModelSettings(
    override val id: Long,
    override val modelId: Long,
    override val name: String,
    val dimensions: Int? = null,
    val encodingFormat: String? = null,
    override val customParams: JsonObject? = null
) : ModelSettings() {
    @Transient
    override val modelType: LLMModelType = LLMModelType.EMBEDDING

    init {
        require(!name.isBlank()) { "Settings name cannot be blank." }
        require(dimensions == null || dimensions > 0) {
            "Embedding dimensions must be positive."
        }
    }
}

/**
 * Settings specifically for [LLMModelType.IMAGE_GENERATION] models. These models create
 * images from text descriptions.
 *
 * @property id Unique identifier for the settings profile (Database PK).
 * @property modelId Foreign key to the associated [LLMModel].
 * @property name The display name of the settings profile (e.g., "Default", "Creative").
 * @property size The desired resolution or dimensions of the generated image (e.g., "1024x1024", "1792x1024").
 * @property quality The quality level of the generated image (e.g., "standard", "hd").
 * @property style The artistic style of the generated image (e.g., "vivid", "natural").
 * @property numImages The number of distinct images to generate in response to a single prompt (e.g., 1 to 4).
 * @property customParams Arbitrary model-specific or provider-specific parameters not covered
 *                        by strongly-typed fields, stored as a [JsonObject].
 */
@Serializable
@SerialName("image_generation")
data class ImageGenerationModelSettings(
    override val id: Long,
    override val modelId: Long,
    override val name: String,
    val size: String? = null,
    val quality: String? = null,
    val style: String? = null,
    val numImages: Int? = null,
    override val customParams: JsonObject? = null
) : ModelSettings() {
    @Transient
    override val modelType: LLMModelType = LLMModelType.IMAGE_GENERATION

    init {
        require(!name.isBlank()) { "Settings name cannot be blank." }
        require(numImages == null || numImages > 0) {
            "Number of images must be positive."
        }
    }
}

/**
 * Settings specifically for [LLMModelType.SPEECH_TO_TEXT] models. These models transcribe
 * spoken language from audio into written text.
 *
 * @property id Unique identifier for the settings profile (Database PK).
 * @property modelId Foreign key to the associated [LLMModel].
 * @property name The display name of the settings profile (e.g., "Default", "Creative").
 * @property language The primary language of the audio content being transcribed (e.g., "en-US", "es-ES").
 * @property responseFormat The desired format for the transcription output (e.g., "json", "text", "srt", "vtt").
 * @property prompt An optional text to guide the model's transcription, such as common words, names,
 *                     or technical terms that might appear in the audio.
 * @property customParams Arbitrary model-specific or provider-specific parameters not covered
 *                        by strongly-typed fields, stored as a [JsonObject].
 */
@Serializable
@SerialName("speech_to_text")
data class SpeechToTextModelSettings(
    override val id: Long,
    override val modelId: Long,
    override val name: String,
    val language: String? = null,
    val responseFormat: String? = null,
    val prompt: String? = null,
    override val customParams: JsonObject? = null
) : ModelSettings() {
    @Transient
    override val modelType: LLMModelType = LLMModelType.SPEECH_TO_TEXT

    init {
        require(!name.isBlank()) { "Settings name cannot be blank." }
    }
}

/**
 * Settings specifically for [LLMModelType.TEXT_TO_SPEECH] models. These models convert
 * written text into synthesized spoken audio.
 *
 * @property id Unique identifier for the settings profile (Database PK).
 * @property modelId Foreign key to the associated [LLMModel].
 * @property name The display name of the settings profile (e.g., "Default", "Creative").
 * @property voice The specific voice to use for speech synthesis (e.g., "alloy", "echo", "fable").
 * @property responseFormat The desired format for the audio output (e.g., "mp3", "opus", "flac").
 * @property speed The speaking rate of the generated audio (e.g., 1.0 for normal speed, 1.5 for faster).
 * @property customParams Arbitrary model-specific or provider-specific parameters not covered
 *                        by strongly-typed fields, stored as a [JsonObject].
 */
@Serializable
@SerialName("text_to_speech")
data class TextToSpeechModelSettings(
    override val id: Long,
    override val modelId: Long,
    override val name: String,
    val voice: String? = null,
    val responseFormat: String? = null,
    val speed: Float? = null,
    override val customParams: JsonObject? = null
) : ModelSettings() {
    @Transient
    override val modelType: LLMModelType = LLMModelType.TEXT_TO_SPEECH

    init {
        require(!name.isBlank()) { "Settings name cannot be blank." }
        require(speed == null || speed > 0f) {
            "Speed must be positive."
        }
    }
}

/**
 * Settings specifically for [LLMModelType.AQA] models. These models are fine-tuned to answer
 * questions by grounding their responses in provided source documents.
 *
 * @property id Unique identifier for the settings profile (Database PK).
 * @property modelId Foreign key to the associated [LLMModel].
 * @property name The display name of the settings profile (e.g., "Default", "Creative").
 * @property maxSources The maximum number of source documents or passages the model should
 *                        consider when generating an answer.
 * @property answerabilityThreshold A confidence threshold (e.g., 0.0 to 1.0) below which the
 *                                    model might indicate that it cannot provide a sufficiently
 *                                    grounded answer.
 * @property customParams Arbitrary model-specific or provider-specific parameters not covered
 *                        by strongly-typed fields, stored as a [JsonObject].
 */
@Serializable
@SerialName("aqa")
data class AqaModelSettings(
    override val id: Long,
    override val modelId: Long,
    override val name: String,
    val maxSources: Int? = null,
    val answerabilityThreshold: Float? = null,
    override val customParams: JsonObject? = null
) : ModelSettings() {
    @Transient
    override val modelType: LLMModelType = LLMModelType.AQA

    init {
        require(!name.isBlank()) { "Settings name cannot be blank." }
        require(maxSources == null || maxSources > 0) {
            "Max sources must be positive."
        }
        require(answerabilityThreshold == null || (answerabilityThreshold >= 0f && answerabilityThreshold <= 1f)) {
            "Answerability threshold must be between 0.0 and 1.0 (inclusive)."
        }
    }
}